# Key Issues Identified
Base64 Encoding Problems
Your current approach sends the entire file as base64 text, which:

Converts binary data to text (inflates size by ~33%)
Loses document structure and formatting
Makes it harder for AI to understand the content

Better Approach: Direct File Upload
## Instead of this (your current approach):
file_base64 = base64.b64encode(file_content).decode('utf-8')
response = model.generate_content([{"text": f"analyze this: {file_base64}"}])
# Do this (direct upload):
uploaded_file = genai.upload_file(temp_file_path)
response = model.generate_content([uploaded_file, prompt])

## Simple Fix for the Current Code
"""
Simple fix for your existing Gemini resume validator
Just replace the validation method with this improved version
"""

import tempfile
import os
import google.generativeai as genai

async def validate_document_as_resume(self, file_content: bytes, filename: str) -> Dict[str, Any]:
    """
    IMPROVED VERSION: Use direct file upload instead of base64
    """
    # Input validation (keep your existing logic)
    validation_error = self._validate_input(file_content, filename)
    if validation_error:
        return validation_error

    try:
        # Create temporary file
        file_extension = filename.split('.')[-1].lower()
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            temp_file.write(file_content)
            temp_file.flush()
            
            try:
                # Upload file directly to Gemini (MUCH better than base64)
                uploaded_file = genai.upload_file(temp_file.name, display_name=filename)
                
                # Use your existing prompt but simplified
                prompt = """
Analyze this document to determine if it's a professional resume/CV.

Respond ONLY with valid JSON:
{
    "is_resume": true/false,
    "likelihood": 0-100,
    "confidence": "high/medium/low",
    "reason": "brief explanation"
}

Resume indicators:
- Personal contact info (name, email, phone)
- Work experience/employment history  
- Education section
- Skills or qualifications
- Professional summary

Scoring:
- 70-100: Clear resume
- 50-69: Likely resume
- 30-49: Uncertain
- 0-29: Not resume

Rules:
- is_resume: true if likelihood >= 40 (be permissive)
- Consider various resume formats
- International styles acceptable
"""

                response = self.gemini_model.generate_content(
                    [uploaded_file, prompt],
                    generation_config={
                        "response_mime_type": "application/json",
                        "temperature": 0.1,  # Slightly less rigid
                        "max_output_tokens": 150
                    }
                )
                
                # Clean up
                genai.delete_file(uploaded_file.name)
                
                return self._process_response(response)
                
            finally:
                # Always clean up temp file
                os.unlink(temp_file.name)

    except Exception as e:
        self.logger.exception(f"Gemini validation failed: {str(e)}")
        # More permissive fallback
        return {
            "is_resume": True,  # Give benefit of doubt
            "likelihood": 50,
            "reason": "Validation failed - assuming resume",
            "confidence": "low", 
            "validation_method": "error_fallback"
        }

def _process_response(self, response) -> Dict[str, Any]:
    """
    IMPROVED: Better JSON parsing with fallbacks
    """
    if not response or not response.text:
        return self._permissive_fallback()
        
    try:
        response_text = response.text.strip()
        
        # Remove markdown formatting
        if response_text.startswith('```'):
            response_text = response_text.replace('```json', '').replace('```', '').strip()
        
        # Try direct parsing first
        try:
            result = json.loads(response_text)
        except json.JSONDecodeError:
            # Extract JSON from text
            import re
            json_match = re.search(r'\{[^}]*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
            else:
                raise ValueError("No JSON found")
        
        # Validate and normalize
        likelihood = max(0, min(100, int(result.get("likelihood", 0))))
        is_resume = bool(result.get("is_resume", likelihood >= 40))  # Lowered threshold
        
        confidence = "low"
        if likelihood >= 70:
            confidence = "high"
        elif likelihood >= 50:
            confidence = "medium"
            
        return {
            "is_resume": is_resume,
            "likelihood": likelihood,
            "confidence": confidence,
            "reason": result.get("reason", "Analysis completed")[:80],
            "validation_method": "gemini_direct_upload"
        }
        
    except Exception as e:
        self.logger.warning(f"Response parsing failed: {str(e)}")
        return self._permissive_fallback()

def _permissive_fallback(self) -> Dict[str, Any]:
    """Give benefit of doubt when validation fails"""
    return {
        "is_resume": True,  # Changed from False
        "likelihood": 50,
        "confidence": "low",
        "reason": "Parsing failed - assuming resume",
        "validation_method": "permissive_fallback"
    }
    