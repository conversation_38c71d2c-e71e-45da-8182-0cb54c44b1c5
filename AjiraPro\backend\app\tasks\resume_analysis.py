"""
Resume Analysis Celery Tasks

Handles asynchronous processing of resume parsing and scoring
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID

from celery import current_task
from ..celery_worker import celery

# Configure logging for tasks
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from ..services.supabase import supabase
from ..services.document_processor import document_processor
from ..services.claude_parser import claude_parser
from ..services.resume_validator import ai_resume_validator
from ..services.openai_content_analyzer import OpenAIContentAnalyzer

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def parse_resume_task(self, resume_id: str, file_path: str):
    """
    Parse resume content using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
        file_path: Path to the resume file in Supabase storage
    """
    try:
        logger.info(f"🚀 Starting resume parsing for resume_id: {resume_id}")

        # Update status to validating first
        logger.info(f"📊 Updating status to 'validating' for resume {resume_id}")
        supabase.table("resumes").update({
            "validation_status": "validating"
        }).eq("id", resume_id).execute()

        # Download file from Supabase storage
        logger.info(f"📥 Downloading file from Supabase: {file_path}")
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            logger.error(f"❌ Failed to download file from Supabase: {file_path}")
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Fast AI-based resume validation using Gemini 1.5 Flash
        logger.info(f"Validating document as resume using Gemini AI: {file_path}")
        filename = file_path.split('/')[-1]  # Get filename from path

        try:
            # Check if GEMINI_API_KEY is available
            from ..core.config import settings
            if settings.GEMINI_API_KEY:
                logger.info("GEMINI_API_KEY found, attempting Gemini validation")
                validation_result = asyncio.run(ai_resume_validator.validate_document_as_resume(
                    file_response, filename
                ))

                logger.info(f"Gemini validation completed: {validation_result}")

                # Store validation results in database
                validation_update = {
                    "validation_status": "validated",
                    "is_resume": validation_result["is_resume"],
                    "validation_likelihood": validation_result["likelihood"],
                    "validation_reason": validation_result["reason"],
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }
                supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()

                # Check if document is a resume
                if not validation_result["is_resume"]:
                    logger.warning(f"Document is not a resume: {validation_result['reason']}")
                    # Don't update parsing_status - leave it as 'pending' since we're not parsing
                    # The overall_status logic will handle this case via validation_status and is_resume

                    return {
                        "status": "not_resume",
                        "resume_id": resume_id,
                        "reason": validation_result["reason"],
                        "likelihood": validation_result["likelihood"]
                    }

                logger.info(
                    f"Document validated as resume: {validation_result['likelihood']}% likelihood"
                )
            else:
                logger.warning("GEMINI_API_KEY not found, using fallback validation")
                # Use simple fallback validation
                validation_result = {
                    "is_resume": True,
                    "likelihood": 95,
                    "reason": "Fallback validation - GEMINI_API_KEY not configured"
                }

                # Store fallback validation results
                validation_update = {
                    "validation_status": "validated",
                    "is_resume": True,
                    "validation_likelihood": 95,
                    "validation_reason": "Fallback validation - GEMINI_API_KEY not configured",
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }
                supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()
                logger.info(f"Using fallback validation: {validation_result}")

        except Exception as validation_error:
            logger.error(f"Gemini validation failed: {str(validation_error)}")

            # Store validation error in database
            validation_update = {
                "validation_status": "failed",
                "validation_error": str(validation_error),
                "validated_at": datetime.now().isoformat()
            }
            supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()

            # Use fallback validation to continue processing
            validation_result = {
                "is_resume": True,
                "likelihood": 90,
                "reason": "Fallback validation - Gemini validation failed"
            }

            # Update with fallback results
            fallback_update = {
                "validation_status": "validated",
                "is_resume": True,
                "validation_likelihood": 90,
                "validation_reason": "Fallback validation - Gemini validation failed"
            }
            supabase.table("resumes").update(fallback_update).eq("id", resume_id).execute()
            logger.warning(f"Using fallback validation due to error: {validation_result}")

        # Now proceed with direct analysis (validation completed)
        logger.info(f"Validation completed, proceeding with direct analysis for resume {resume_id}")

        # Update status to parsing
        supabase.table("resumes").update({
            "parsing_status": "parsing"
        }).eq("id", resume_id).execute()

        # Use new direct analysis method: format scoring + content parsing in one step
        logger.info(f"🤖 Performing direct analysis with Claude for resume_id: {resume_id}")
        format_score, format_feedback, parsed_content = asyncio.run(
            claude_parser.analyze_resume_file_directly(file_response, filename)
        )

        logger.info(f"✅ Direct analysis completed for resume {resume_id}")
        logger.info(f"📊 Format score: {format_score}")
        logger.info(f"📋 Parsed sections: {list(parsed_content.keys())}")

        # Store both parsing and scoring results in database
        update_data = {
            "parsed_content": json.dumps(parsed_content),
            "parsing_status": "parsed",
            "parsed_at": datetime.now().isoformat(),
            "parsing_error": None,
            # Format scoring results
            "claude_score": format_score,
            "claude_feedback": json.dumps(format_feedback),
            "scoring_status": "scored",
            "last_scored_at": datetime.now().isoformat(),
            "scoring_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully completed direct analysis for resume {resume_id}")

        # Queue content analysis task (OpenAI) directly - skip separate format scoring
        analyze_content_task.delay(resume_id)

        return {
            "status": "success",
            "resume_id": resume_id,
            "parsed_sections": list(parsed_content.keys()),
            "format_score": format_score
        }

    except Exception as e:
        logger.error(f"Resume parsing task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "parsing_status": "failed",
            "parsing_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def score_format_task(self, resume_id: str):
    """
    Score resume format and structure using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting format scoring for resume_id: {resume_id}")

        # Update scoring status
        supabase.table("resumes").update({
            "scoring_status": "scoring"
        }).eq("id", resume_id).execute()

        # Get resume data including parsed content
        resume_response = supabase.table("resumes").select(
            "parsed_content, file_path"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        parsed_content = json.loads(resume_data["parsed_content"])

        # Download original file to get text for format analysis
        file_path = resume_data["file_path"]
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract text from document
        filename = file_path.split('/')[-1]
        extracted_data = document_processor.extract_text_from_file(file_response, filename)
        original_text = extracted_data["text"]

        # Score format using Claude
        logger.info(f"Scoring format with Claude for resume_id: {resume_id}")
        claude_score, claude_feedback = asyncio.run(claude_parser.score_format_and_structure(
            parsed_content,
            original_text
        ))

        # Update resume with Claude scoring results
        update_data = {
            "claude_score": claude_score,
            "claude_feedback": json.dumps(claude_feedback),
            "scoring_status": "scored",
            "last_scored_at": datetime.now().isoformat(),
            "scoring_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully scored format for resume {resume_id}, score: {claude_score}")

        # Queue content analysis task
        analyze_content_task.delay(resume_id)

        return {
            "status": "success",
            "resume_id": resume_id,
            "claude_score": claude_score,
            "feedback_items": len(claude_feedback.get("format_issues", []))
        }

    except Exception as e:
        logger.error(f"Format scoring task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "scoring_status": "failed",
            "scoring_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def analyze_content_task(self, resume_id: str):
    """
    Analyze resume content quality using OpenAI GPT-4

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"🤖 Starting content analysis for resume_id: {resume_id}")

        # Check if OpenAI API key is configured
        from ..core.config import settings
        if not settings.OPENAI_API_KEY:
            logger.warning(f"⚠️ OpenAI API key not configured, skipping content analysis for {resume_id}")
            # Update status to completed without OpenAI analysis
            supabase.table("resumes").update({
                "content_analysis_status": "completed",
                "content_analysis_error": "OpenAI API key not configured",
                "content_analyzed_at": datetime.now().isoformat()
            }).eq("id", resume_id).execute()
            return {
                "status": "skipped",
                "resume_id": resume_id,
                "reason": "OpenAI API key not configured"
            }

        # Update content analysis status
        logger.info(f"📊 Updating content analysis status to 'analyzing' for resume {resume_id}")
        supabase.table("resumes").update({
            "content_analysis_status": "analyzing"
        }).eq("id", resume_id).execute()

        # Get resume data including parsed content
        resume_response = supabase.table("resumes").select(
            "parsed_content"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        parsed_content = json.loads(resume_data["parsed_content"])

        # Initialize OpenAI content analyzer
        logger.info(f"🔧 Initializing OpenAI content analyzer for resume {resume_id}")
        content_analyzer = OpenAIContentAnalyzer()

        # Analyze content using OpenAI
        logger.info(f"🤖 Analyzing content with OpenAI for resume_id: {resume_id}")
        logger.info(f"📄 Parsed content sections: {list(parsed_content.keys())}")

        content_score, content_feedback = asyncio.run(content_analyzer.analyze_content_quality(
            parsed_content
        ))

        logger.info(f"✅ OpenAI analysis completed for resume {resume_id}, score: {content_score}")

        # Get Claude score to calculate overall score
        resume_response = supabase.table("resumes").select("claude_score").eq("id", resume_id).execute()
        claude_score = resume_response.data[0].get("claude_score", 0) if resume_response.data else 0

        # Calculate overall score (weighted average: 40% format, 60% content)
        overall_score = round((claude_score * 0.4) + (content_score * 0.6), 1)
        logger.info(f"📊 Calculated overall score for resume {resume_id}: {overall_score} (Claude: {claude_score}, OpenAI: {content_score})")

        # Update resume with OpenAI content analysis results and overall score
        update_data = {
            "content_quality_score": content_score,
            "openai_feedback": json.dumps(content_feedback),
            "content_analysis_status": "completed",
            "content_analyzed_at": datetime.now().isoformat(),
            "content_analysis_error": None,
            "overall_score": overall_score
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully analyzed content for resume {resume_id}, score: {content_score}")

        return {
            "status": "success",
            "resume_id": resume_id,
            "content_score": content_score,
            "feedback_items": len(content_feedback.get("content_issues", []))
        }

    except Exception as e:
        logger.error(f"❌ Content analysis task failed for {resume_id}: {str(e)}")

        # Update status to failed but don't block the overall workflow
        supabase.table("resumes").update({
            "content_analysis_status": "failed",
            "content_analysis_error": str(e),
            "content_analyzed_at": datetime.now().isoformat()
        }).eq("id", resume_id).execute()

        # Don't raise the exception - let the workflow continue without OpenAI analysis
        logger.warning(f"⚠️ Content analysis failed for {resume_id}, but workflow will continue")
        return {
            "status": "failed",
            "resume_id": resume_id,
            "error": str(e)
        }

@celery.task(bind=True)
def analyze_resume_task(self, resume_id: str):
    """
    Complete resume analysis workflow - parsing and scoring

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"🚀 Starting complete analysis for resume_id: {resume_id}")

        # Get resume record to get file path
        logger.info(f"📋 Fetching resume data from database for ID: {resume_id}")
        resume_response = supabase.table("resumes").select("file_path").eq("id", resume_id).execute()

        if not resume_response.data:
            logger.error(f"❌ Resume {resume_id} not found in database")
            raise Exception(f"Resume {resume_id} not found")

        file_path = resume_response.data[0]["file_path"]
        if not file_path:
            logger.error(f"❌ No file path found for resume {resume_id}")
            raise Exception(f"No file path found for resume {resume_id}")

        logger.info(f"📁 File path found: {file_path}")

        # Start with parsing
        logger.info(f"🔄 Queuing parse_resume_task for resume {resume_id}")
        parse_result = parse_resume_task.delay(resume_id, file_path)
        logger.info(f"✅ Parse task queued with ID: {parse_result.id}")

        return {
            "status": "started",
            "resume_id": resume_id,
            "parse_task_id": parse_result.id
        }

    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise

@celery.task
def get_analysis_status(resume_id: str) -> Dict[str, Any]:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume record

    Returns:
        Dict with current status information
    """
    try:
        resume_response = supabase.table("resumes").select(
            "validation_status, is_resume, validation_reason, parsing_status, scoring_status, content_analysis_status, validated_at, parsed_at, last_scored_at, content_analyzed_at, parsing_error, scoring_error, validation_error, content_analysis_error"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            return {"error": f"Resume {resume_id} not found"}

        data = resume_response.data[0]

        return {
            "resume_id": resume_id,
            "validation_status": data.get("validation_status"),
            "is_resume": data.get("is_resume"),
            "validation_reason": data.get("validation_reason"),
            "parsing_status": data["parsing_status"],
            "scoring_status": data["scoring_status"],
            "content_analysis_status": data.get("content_analysis_status", "pending"),
            "validated_at": data.get("validated_at"),
            "parsed_at": data["parsed_at"],
            "last_scored_at": data["last_scored_at"],
            "content_analyzed_at": data.get("content_analyzed_at"),
            "parsing_error": data["parsing_error"],
            "scoring_error": data["scoring_error"],
            "validation_error": data.get("validation_error"),
            "content_analysis_error": data.get("content_analysis_error"),
            "overall_status": _determine_overall_status(data)
        }

    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        return {"error": str(e)}

def _determine_overall_status(data: Dict[str, Any]) -> str:
    """Determine overall analysis status based on validation, parsing, scoring, and content analysis status"""
    validation_status = data.get("validation_status", "pending")
    is_resume = data.get("is_resume")
    parsing_status = data.get("parsing_status", "pending")
    scoring_status = data.get("scoring_status", "pending")
    content_analysis_status = data.get("content_analysis_status", "pending")

    # Debug logging to track status determination
    logger.info(f"🔍 Status determination - validation: {validation_status}, is_resume: {is_resume} (type: {type(is_resume)}), parsing: {parsing_status}, scoring: {scoring_status}, content: {content_analysis_status}")

    # Validation phase
    if validation_status == "pending":
        return "pending"
    elif validation_status == "validating":
        return "validating"
    elif validation_status == "failed":
        return "validation_failed"
    elif validation_status == "validated" and not is_resume:
        return "not_resume"

    # Parsing phase (only if validation passed and is_resume = true)
    if parsing_status == "failed":
        return "failed"
    elif parsing_status == "parsing":
        return "parsing"
    elif parsing_status == "parsed" and scoring_status == "pending":
        return "ready_for_scoring"

    # Scoring phase
    elif scoring_status == "scoring":
        logger.info(f"🔄 Returning status: scoring")
        return "scoring"
    elif scoring_status == "scored" and content_analysis_status == "pending":
        logger.info(f"🔄 Returning status: ready_for_content_analysis")
        return "ready_for_content_analysis"
    elif scoring_status == "failed":
        logger.info(f"❌ Returning status: scoring_failed")
        return "scoring_failed"

    # Content analysis phase
    elif content_analysis_status == "analyzing":
        logger.info(f"🔄 Returning status: analyzing_content")
        return "analyzing_content"
    elif content_analysis_status == "completed":
        logger.info(f"✅ Returning status: completed")
        return "completed"
    elif content_analysis_status == "failed":
        logger.info(f"❌ Returning status: content_analysis_failed")
        return "content_analysis_failed"
    else:
        logger.info(f"🔄 Returning default status: pending")
        return "pending"
