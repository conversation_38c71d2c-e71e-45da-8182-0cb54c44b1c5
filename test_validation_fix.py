#!/usr/bin/env python3
"""
Test script to verify the fixed Gemini validation works correctly
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
backend_path = os.path.join(os.path.dirname(__file__), '<PERSON><PERSON>raP<PERSON>', 'backend')
sys.path.append(backend_path)

from app.services.resume_validator import GeminiResumeValidator

# Sample resume content that should be accepted
SAMPLE_RESUME_CONTENT = """
<PERSON>
520 Mission St, Santa Clara, CA, 95053
<EMAIL> | ************

Qualifications and Capabilities
• Excellent time management, problem solving, organizational, and communication skills.
• Proficient in utilizing online social media and Microsoft Office
• Familiar with HTML, PHP, CSS, C, and C++

Education
Santa Clara University                                                Santa Clara, CA | 2011-Present
Bachelor of Science in Computer Science and Engineering                    Anticipated June 2015
• Cisco-SCU Engineering Fellowship                                                      2012

Work Experience
Alpha Kappa Psi: Psi Omega Chapter                                   Santa Clara, CA | June 2012 – Present
IT Committee
• Exemplified leadership skills to delegate tasks and creativity to design the website layout using HTML
  and CSS skills.
• Currently designing online application process through the website for fraternity membership in order to
  streamline the rush process.

InnoVentions: Connect2Dine                                           Santa Clara, CA | December 2011 – March 2012
Intern
• Designed product wireframe using HTML, PHP, and CSS
• Advised the development of the usability interface of the website landing page and home page.
• Demonstrated team-work, communication, and organizational skills by working with programmers and
  business administrators during the design and implementation of product wireframe.

Mercer Trade Inc.                                                    Mercer Island, WA | September 2010– June 2011
Marketing
• Managed the event planning of a fundraising event for Seattle Adaptive Sports in which the class raised.
• Directed marketing campaigns during the school year to raise awareness about products that the company
  imported and the charity events that we held.
• Demonstrated leadership, customer service, communication, and organizational skills while planning
  events, delegating tasks, selling products, and running fundraising events.

Mercer Island High School Student Store                             Mercer Island, WA | September 2009 – June 2011
Manager
• Directed the designation of work to other students, and ran the daily operations of the business.
• Demonstrated detail-orientation, management skills, and the ability to handle customers in a professional
  manner.

Leadership and Volunteer Experience
Alpha Kappa Psi: Psi Omega Chapter                                                    April 2012 – June 2012
Fundraising Committee Chair: Omicron Pledge Class
• Facilitated and oversaw fundraising activities for the Omicron pledge class demonstrating management,
  communication, and time management skills.
• Raised $2200 over six weeks for the chapter.
"""

async def test_validation():
    """Test the fixed Gemini validation"""
    print("🧪 Testing Fixed Gemini Resume Validation")
    print("=" * 50)
    
    try:
        # Initialize validator
        validator = GeminiResumeValidator()
        print("✅ Gemini validator initialized")
        
        # Test with the Eric Chang resume content
        print("\n📄 Testing with Eric Chang resume content...")
        
        resume_bytes = SAMPLE_RESUME_CONTENT.encode('utf-8')
        result = await validator.validate_document_as_resume(
            resume_bytes, 
            "Eric Chang resume.pdf"
        )
        
        print(f"\n📊 Validation Results:")
        print(f"   Is Resume: {result['is_resume']}")
        print(f"   Likelihood: {result['likelihood']}%")
        print(f"   Confidence: {result['confidence']}")
        print(f"   Reason: {result['reason']}")
        print(f"   Method: {result['validation_method']}")
        
        # Check if the result is what we expect
        if result['is_resume']:
            print("\n✅ SUCCESS: Resume correctly identified!")
        else:
            print(f"\n❌ FAILED: Resume incorrectly rejected (likelihood: {result['likelihood']}%)")
            print(f"   Reason: {result['reason']}")
            
        return result['is_resume']
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_validation())
    if success:
        print("\n🎉 Validation fix appears to be working!")
    else:
        print("\n⚠️  Validation may still need adjustment.")
