"""
Enhanced Error Handling Utilities

Provides comprehensive error handling, logging, and monitoring for AI services.
"""

import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ErrorHandler:
    """
    Centralized error handling and logging
    """
    
    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """
        Determine if an error is retryable
        
        Args:
            error: The exception to check
            
        Returns:
            True if the error should be retried
        """
        error_str = str(error).lower()
        
        # Service unavailability errors (temporary)
        if any(keyword in error_str for keyword in [
            "service unavailable", "529", "502", "503", "504",
            "rate limit", "timeout", "connection", "network"
        ]):
            return True
            
        # Specific AI service errors that are retryable
        if any(keyword in error_str for keyword in [
            "overloaded", "busy", "temporarily unavailable",
            "try again", "rate exceeded"
        ]):
            return True
            
        return False
    
    @staticmethod
    def get_retry_delay(attempt: int, base_delay: int = 60) -> int:
        """
        Calculate exponential backoff delay
        
        Args:
            attempt: Current retry attempt (0-based)
            base_delay: Base delay in seconds
            
        Returns:
            Delay in seconds
        """
        return min(base_delay * (2 ** attempt), 600)  # Max 10 minutes
    
    @staticmethod
    def log_error(error: Exception, context: Dict[str, Any], level: str = "error"):
        """
        Log error with comprehensive context
        
        Args:
            error: The exception that occurred
            context: Additional context information
            level: Log level (error, warning, info)
        """
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "is_retryable": ErrorHandler.is_retryable_error(error),
            "timestamp": datetime.now().isoformat(),
            "context": context,
            "traceback": traceback.format_exc() if level == "error" else None
        }
        
        log_message = f"Error in {context.get('operation', 'unknown')}: {error_info['error_message']}"
        
        if level == "error":
            logger.error(log_message, extra=error_info)
        elif level == "warning":
            logger.warning(log_message, extra=error_info)
        else:
            logger.info(log_message, extra=error_info)
    
    @staticmethod
    def create_error_response(error: Exception, resume_id: str, operation: str) -> Dict[str, Any]:
        """
        Create standardized error response
        
        Args:
            error: The exception that occurred
            resume_id: ID of the resume being processed
            operation: Name of the operation that failed
            
        Returns:
            Standardized error response
        """
        return {
            "status": "error",
            "resume_id": resume_id,
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "is_retryable": ErrorHandler.is_retryable_error(error),
            "timestamp": datetime.now().isoformat()
        }

def handle_ai_service_error(func):
    """
    Decorator for handling AI service errors consistently
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            context = {
                "function": func.__name__,
                "args": str(args)[:200],  # Limit length
                "kwargs": str(kwargs)[:200]
            }
            ErrorHandler.log_error(e, context)
            raise
    return wrapper
