These symptoms point to several common web application issues that can cause poor responsiveness and state management problems. Here are the likely culprits:
State Management Issues
Your infinite loading on dashboard clicks and disappearing profile sections suggest client-side state corruption. This often happens when:

State isn't properly cleared after operations
Asynchronous operations don't handle errors correctly
Component state gets into an inconsistent condition

URL Routing Problems
The URL changing from /resume-optimizer to /?p=/resume-optimizer on refresh indicates routing configuration issues:

Your client-side router isn't properly configured for direct URL access
Server isn't serving your SPA correctly for non-root routes
Missing proper history API handling

Session/Memory Leaks
The fact that clearing browser history "fixes" it temporarily suggests:

Memory leaks accumulating over user sessions
Local storage corruption or improper cleanup
Service worker caching gone wrong

Common Technical Causes
Frontend Issues:

Race conditions in async operations
Improper error handling in API calls
State not being reset between route changes
Event listeners not being properly cleaned up

Backend Issues:

Database connections not being properly closed (PLpgSQL suggests PostgreSQL)
Session management problems
API endpoints hanging or timing out