import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTheme } from "../contexts/ThemeContext";
import { useAuth } from "../contexts/AuthContext";
import Button from "../components/ui/Button";
import { motion } from "framer-motion";
import { getResume, getAnalysisStatus, getClaudeFeedback, getOpenAIFeedback, startResumeAnalysis } from "../utils/resumeUtils";
import EnhancedPDFViewer from "../components/EnhancedPDFViewer";

// Resume data interface
interface ResumeData {
  id: string;
  title: string;
  file_path?: string | null;
  // Analysis status fields
  parsing_status: string;
  scoring_status: string;
  parsed_at?: string | null;
  last_scored_at?: string | null;
  // Claude scoring fields
  claude_score?: number | null;
  claude_feedback?: any | null;
  // OpenAI scoring fields (for future Phase 2.2)
  openai_score?: number | null;
  overall_score?: number | null;
  career_overview_score?: number | null;
  experience_score?: number | null;
  education_score?: number | null;
  additional_qualifications_score?: number | null;
  content_quality_score?: number | null;
  // Legacy field for backward compatibility
  ats_score?: number | null;
}

// Analysis status interface
interface AnalysisStatus {
  resume_id: string;
  validation_status?: string;
  is_resume?: boolean;
  validation_reason?: string;
  parsing_status: string;
  scoring_status: string;
  content_analysis_status?: string;
  validated_at?: string | null;
  parsed_at?: string | null;
  last_scored_at?: string | null;
  content_analyzed_at?: string | null;
  parsing_error?: string | null;
  scoring_error?: string | null;
  content_analysis_error?: string | null;
  overall_status: string;
}

// Claude feedback interface
interface ClaudeFeedback {
  resume_id: string;
  claude_score: number;
  claude_feedback: any;
  scoring_status: string;
}

// OpenAI feedback interface
interface OpenAIFeedback {
  resume_id: string;
  content_quality_score: number;
  openai_feedback: any;
  content_analysis_status: string;
}

// Note: getDefaultResumeData removed as it's no longer needed with real data from API

// Enhanced Claude Feedback Component with expandable items
interface ClaudeFeedbackSectionProps {
  claudeFeedback: ClaudeFeedback;
  isDark: boolean;
}

// OpenAI Feedback Component with expandable items
interface OpenAIFeedbackSectionProps {
  openAIFeedback: OpenAIFeedback;
  isDark: boolean;
}

const ClaudeFeedbackSection: React.FC<ClaudeFeedbackSectionProps> = ({ claudeFeedback, isDark }) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  // Handle different possible structures of claude_feedback
  let formatIssues: any[] = [];

  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('🔍 Claude feedback structure:', claudeFeedback.claude_feedback);
    // eslint-disable-next-line no-console
    console.log('🔍 Claude feedback type:', typeof claudeFeedback.claude_feedback);
  }

  if (claudeFeedback.claude_feedback.format_issues) {
    formatIssues = claudeFeedback.claude_feedback.format_issues;
  } else if (claudeFeedback.claude_feedback.detailed_feedback) {
    formatIssues = claudeFeedback.claude_feedback.detailed_feedback;
  } else if (Array.isArray(claudeFeedback.claude_feedback)) {
    formatIssues = claudeFeedback.claude_feedback;
  }

  // Sort by severity: high -> medium -> low
  const severityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
  const sortedIssues = formatIssues.sort((a, b) =>
    (severityOrder[a.severity as keyof typeof severityOrder] || 3) -
    (severityOrder[b.severity as keyof typeof severityOrder] || 3)
  );

  // Limit to 5 items to prevent overwhelming UI and save space for OpenAI content analysis
  const displayedIssues = sortedIssues.slice(0, 5);
  const hiddenCount = sortedIssues.length - displayedIssues.length;

  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('🔍 Format issues found:', displayedIssues);
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'border-red-500';
      case 'medium': return 'border-orange-500';
      case 'low': return 'border-green-500';
      default: return 'border-gray-400';
    }
  };

  const getSeverityBg = (severity: string) => {
    switch (severity) {
      case 'high': return isDark ? 'bg-red-900/20' : 'bg-red-50';
      case 'medium': return isDark ? 'bg-orange-900/20' : 'bg-orange-50';
      case 'low': return isDark ? 'bg-green-900/20' : 'bg-green-50';
      default: return isDark ? 'bg-gray-800' : 'bg-gray-50';
    }
  };

  return (
    <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-1 flex flex-col ${isDark ? "bg-dark-200" : "bg-white"}`}>
      <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
        Format Recommendations
        {displayedIssues.length > 0 && (
          <span className={`ml-2 text-sm font-normal ${isDark ? "text-gray-400" : "text-gray-500"}`}>
            ({displayedIssues.length} {displayedIssues.length === 1 ? 'issue' : 'issues'}{hiddenCount > 0 ? `, ${hiddenCount} more` : ''})
          </span>
        )}
      </h2>
      <div className="p-6 flex-1 overflow-y-auto">
        {displayedIssues.length > 0 ? (
          <div className="space-y-3">
            {displayedIssues.map((issue: any, index: number) => (
              <div
                key={index}
                className={`border-l-4 ${getSeverityColor(issue.severity)} ${getSeverityBg(issue.severity)} rounded-r-lg overflow-hidden transition-all duration-200`}
              >
                <button
                  onClick={() => toggleExpanded(index)}
                  className={`w-full p-4 text-left hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <div className={`flex-shrink-0 w-2 h-2 rounded-full mr-3 ${
                        issue.severity === 'high' ? 'bg-red-500' :
                        issue.severity === 'medium' ? 'bg-orange-500' :
                        'bg-green-500'
                      }`}></div>
                      <h4 className={`font-medium ${isDark ? "text-white" : "text-gray-800"}`}>
                        {issue.issue}
                      </h4>
                    </div>
                    <svg
                      className={`w-5 h-5 transition-transform duration-200 ${
                        expandedItems.has(index) ? 'rotate-180' : ''
                      } ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {expandedItems.has(index) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="px-4 pb-4"
                  >
                    {issue.recommendation && (
                      <p className={`text-sm mb-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                        <strong>Recommendation:</strong> {issue.recommendation}
                      </p>
                    )}
                    {issue.section && (
                      <span className={`inline-block px-2 py-1 text-xs rounded ${
                        isDark ? "bg-dark-300 text-gray-300" : "bg-gray-200 text-gray-600"
                      }`}>
                        {issue.section.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                    )}
                  </motion.div>
                )}
              </div>
            ))}

            {hiddenCount > 0 && (
              <div className={`text-center py-3 ${isDark ? "text-gray-400" : "text-gray-500"}`}>
                <p className="text-sm">
                  {hiddenCount} additional recommendation{hiddenCount === 1 ? '' : 's'} available.
                  <br />
                  <span className="text-xs">Showing highest priority items first.</span>
                </p>
              </div>
            )}
          </div>
        ) : (
          <p className={`text-center ${isDark ? "text-gray-400" : "text-gray-500"}`}>
            Great! No major format issues found. Your resume follows ATS-friendly formatting guidelines.
          </p>
        )}
      </div>
    </div>
  );
};

const OpenAIFeedbackSection: React.FC<OpenAIFeedbackSectionProps> = ({ openAIFeedback, isDark }) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  // Handle different possible structures of openai_feedback
  let contentIssues: any[] = [];

  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('🔍 OpenAI feedback structure:', openAIFeedback.openai_feedback);
    // eslint-disable-next-line no-console
    console.log('🔍 OpenAI feedback type:', typeof openAIFeedback.openai_feedback);
  }

  if (openAIFeedback.openai_feedback.content_issues) {
    contentIssues = openAIFeedback.openai_feedback.content_issues;
  } else if (openAIFeedback.openai_feedback.detailed_feedback) {
    contentIssues = openAIFeedback.openai_feedback.detailed_feedback;
  } else if (Array.isArray(openAIFeedback.openai_feedback)) {
    contentIssues = openAIFeedback.openai_feedback;
  }

  // Sort by severity: high -> medium -> low
  const severityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
  const sortedIssues = contentIssues.sort((a, b) =>
    (severityOrder[a.severity as keyof typeof severityOrder] || 3) -
    (severityOrder[b.severity as keyof typeof severityOrder] || 3)
  );

  // Limit to 5 items to prevent overwhelming UI and save space
  const displayedIssues = sortedIssues.slice(0, 5);
  const hiddenCount = sortedIssues.length - displayedIssues.length;

  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('🔍 Content issues found:', displayedIssues);
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'border-red-500';
      case 'medium': return 'border-orange-500';
      case 'low': return 'border-green-500';
      default: return 'border-gray-400';
    }
  };

  const getSeverityBg = (severity: string) => {
    switch (severity) {
      case 'high': return isDark ? 'bg-red-900/20' : 'bg-red-50';
      case 'medium': return isDark ? 'bg-orange-900/20' : 'bg-orange-50';
      case 'low': return isDark ? 'bg-green-900/20' : 'bg-green-50';
      default: return isDark ? 'bg-gray-800' : 'bg-gray-50';
    }
  };

  return (
    <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-1 flex flex-col ${isDark ? "bg-dark-200" : "bg-white"}`}>
      <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
        Content Recommendations
        {displayedIssues.length > 0 && (
          <span className={`ml-2 text-sm font-normal ${isDark ? "text-gray-400" : "text-gray-500"}`}>
            ({displayedIssues.length} {displayedIssues.length === 1 ? 'suggestion' : 'suggestions'}{hiddenCount > 0 ? `, ${hiddenCount} more` : ''})
          </span>
        )}
      </h2>
      <div className="p-6 flex-1 overflow-y-auto">
        {displayedIssues.length > 0 ? (
          <div className="space-y-3">
            {displayedIssues.map((issue: any, index: number) => (
              <div
                key={index}
                className={`border-l-4 ${getSeverityColor(issue.severity)} ${getSeverityBg(issue.severity)} rounded-r-lg overflow-hidden transition-all duration-200`}
              >
                <button
                  onClick={() => toggleExpanded(index)}
                  className={`w-full p-4 text-left hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <div className={`flex-shrink-0 w-2 h-2 rounded-full mr-3 ${
                        issue.severity === 'high' ? 'bg-red-500' :
                        issue.severity === 'medium' ? 'bg-orange-500' :
                        'bg-green-500'
                      }`}></div>
                      <h4 className={`font-medium ${isDark ? "text-white" : "text-gray-800"}`}>
                        {issue.issue}
                      </h4>
                    </div>
                    <svg
                      className={`w-5 h-5 transition-transform duration-200 ${
                        expandedItems.has(index) ? 'rotate-180' : ''
                      } ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {expandedItems.has(index) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="px-4 pb-4"
                  >
                    {issue.recommendation && (
                      <p className={`text-sm mb-3 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                        <strong>Recommendation:</strong> {issue.recommendation}
                      </p>
                    )}
                    {issue.current_text && (
                      <div className="mb-3">
                        <p className={`text-xs font-medium mb-1 ${isDark ? "text-gray-400" : "text-gray-500"}`}>
                          Current:
                        </p>
                        <p className={`text-sm italic ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                          "{issue.current_text}"
                        </p>
                      </div>
                    )}
                    {issue.suggested_improvement && (
                      <div className="mb-3">
                        <p className={`text-xs font-medium mb-1 ${isDark ? "text-gray-400" : "text-gray-500"}`}>
                          Suggested:
                        </p>
                        <p className={`text-sm ${isDark ? "text-green-300" : "text-green-700"}`}>
                          "{issue.suggested_improvement}"
                        </p>
                      </div>
                    )}
                    {issue.section && (
                      <span className={`inline-block px-2 py-1 text-xs rounded ${
                        isDark ? "bg-dark-300 text-gray-300" : "bg-gray-200 text-gray-600"
                      }`}>
                        {issue.section.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                    )}
                  </motion.div>
                )}
              </div>
            ))}

            {hiddenCount > 0 && (
              <div className={`text-center py-3 ${isDark ? "text-gray-400" : "text-gray-500"}`}>
                <p className="text-sm">
                  {hiddenCount} additional suggestion{hiddenCount === 1 ? '' : 's'} available.
                  <br />
                  <span className="text-xs">Showing highest priority items first.</span>
                </p>
              </div>
            )}
          </div>
        ) : (
          <p className={`text-center ${isDark ? "text-gray-400" : "text-gray-500"}`}>
            Excellent! Your resume content is well-written and impactful.
          </p>
        )}
      </div>
    </div>
  );
};

const ResumeResultsPage: React.FC = () => {
  const { isDark } = useTheme();
  const { isAuthenticated } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [resumeData, setResumeData] = useState<ResumeData | null>(null);
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus | null>(null);
  const [claudeFeedback, setClaudeFeedback] = useState<ClaudeFeedback | null>(null);
  const [openAIFeedback, setOpenAIFeedback] = useState<OpenAIFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  const searchParams = new URLSearchParams(location.search);
  let resumeId = searchParams.get("id");

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/resume-optimizer", { replace: true });
      return;
    }

    const fetchResumeData = async () => {
      try {
        if (!resumeId) throw new Error("No resume ID provided.");

        // Fetch the resume data
        const resume = await getResume(resumeId);
        setResumeData(resume);

        // Start polling for analysis status
        setIsPolling(true);
        pollAnalysisStatus();

      } catch (err: any) {
        setError(err.message || "Failed to load resume data.");
      } finally {
        setIsLoading(false);
      }
    };

    const pollAnalysisStatus = async () => {
      if (!resumeId) return;

      try {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('🔄 Polling analysis status...');
        }
        const status = await getAnalysisStatus(resumeId);
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('📊 Received status update:', status);
        }
        setAnalysisStatus(status);

        // If status is still pending and no validation has started, trigger analysis
        if (status.overall_status === 'pending' && !status.validation_status) {
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('🚀 Status is pending with no validation - triggering analysis');
          }
          try {
            await startResumeAnalysis(resumeId);
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.log('✅ Analysis triggered successfully, continuing polling...');
            }
            // Continue polling after triggering
            setTimeout(pollAnalysisStatus, 2000);
            return;
          } catch (analysisError: any) {
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.error('❌ Failed to start analysis:', analysisError);
            }
            setError(`Failed to start analysis: ${analysisError.message || 'Please try again.'}`);
            setIsPolling(false);
            return;
          }
        }

        // If analysis is complete, fetch both Claude and OpenAI feedback
        if (status.overall_status === 'completed' || status.overall_status === 'scoring_failed' || status.overall_status === 'content_analysis_failed') {
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('🎉 Analysis completed! Stopping polling and fetching feedback...');
          }
          setIsPolling(false);

          if (status.overall_status === 'completed') {
            try {
              // Fetch Claude feedback
              const claudeFeedbackData = await getClaudeFeedback(resumeId);
              if (process.env.NODE_ENV === 'development') {
                // eslint-disable-next-line no-console
                console.log('📝 Claude feedback received:', claudeFeedbackData);
              }
              setClaudeFeedback(claudeFeedbackData);

              // Fetch OpenAI feedback
              try {
                const openAIFeedbackData = await getOpenAIFeedback(resumeId);
                if (process.env.NODE_ENV === 'development') {
                  // eslint-disable-next-line no-console
                  console.log('🤖 OpenAI feedback received:', openAIFeedbackData);
                }
                setOpenAIFeedback(openAIFeedbackData);
              } catch (openAIError) {
                if (process.env.NODE_ENV === 'development') {
                  // eslint-disable-next-line no-console
                  console.warn('⚠️ Failed to fetch OpenAI feedback:', openAIError);
                }
                // Don't set error for OpenAI failure - Claude feedback is still available
              }
            } catch (feedbackError) {
              // Failed to fetch Claude feedback - continue without it
              if (process.env.NODE_ENV === 'development') {
                // eslint-disable-next-line no-console
                console.warn('⚠️ Failed to fetch Claude feedback:', feedbackError);
              }
              setError('Failed to load detailed feedback. Score is available but recommendations may be limited.');
            }
          }
        } else if (status.overall_status === 'failed') {
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.error('❌ Analysis failed:', status);
          }
          setIsPolling(false);
          setError(`Analysis failed: ${status.parsing_error || status.scoring_error || 'Unknown error'}`);
        } else {
          // Continue polling if analysis is still in progress
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('⏳ Analysis still in progress, continuing to poll in 3 seconds...');
          }
          setTimeout(pollAnalysisStatus, 3000); // Poll every 3 seconds
        }
      } catch (statusError: any) {
        // Failed to get analysis status - stop polling and show error
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.error('❌ Failed to get analysis status:', statusError);
        }
        setIsPolling(false);
        setError('Failed to check analysis status. Please refresh the page to try again.');
      }
    };

    fetchResumeData();
  }, [resumeId, isAuthenticated, navigate]);

  const getScoreColor = (score: number) =>
    score >= 80 ? (isDark ? "text-green-400" : "text-green-600") :
    score >= 60 ? (isDark ? "text-yellow-400" : "text-yellow-600") :
                  (isDark ? "text-red-400" : "text-red-600");

  const getProgressPercentage = () => {
    if (!analysisStatus) return 5;

    switch (analysisStatus.overall_status) {
      case 'pending':
        return 10;
      case 'validating':
        return 25;
      case 'validation_failed':
      case 'not_resume':
        return 25; // Stopped at validation
      case 'parsing':
        return 40;
      case 'ready_for_scoring':
        return 60;
      case 'scoring':
        return 75;
      case 'ready_for_content_analysis':
        return 80;
      case 'analyzing_content':
        return 90;
      case 'completed':
        return 100;
      default:
        return 15;
    }
  };

  const getStatusMessage = () => {
    if (!analysisStatus) return "Initializing analysis...";

    switch (analysisStatus.overall_status) {
      case 'pending':
        return "Preparing to analyze your document...";
      case 'validating':
        return "Validating document...";
      case 'validation_failed':
        return "Document validation failed. Please try again.";
      case 'not_resume':
        return "This document doesn't appear to be a resume.";
      case 'parsing':
        return "Analyzing document content...";
      case 'ready_for_scoring':
        return "Content analyzed! Starting format evaluation...";
      case 'scoring':
        return "Evaluating format and ATS compatibility...";
      case 'ready_for_content_analysis':
        return "Format evaluated! Starting content quality analysis...";
      case 'analyzing_content':
        return "Analyzing content quality and providing recommendations...";
      case 'completed':
        return "Analysis complete!";
      case 'failed':
        return "Analysis failed. Please try again.";
      case 'scoring_failed':
        return "Format evaluation failed. Please try again.";
      case 'content_analysis_failed':
        return "Content analysis failed. Please try again.";
      default:
        return "Processing your document...";
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <svg className="animate-spin w-12 h-12 mx-auto mb-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <h2 className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-800"}`}>
            Loading Resume Analysis
          </h2>
          <p className={`${isDark ? "text-gray-300" : "text-gray-600"}`}>
            Please wait while we prepare your resume analysis...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center max-w-md">
          <svg className="w-16 h-16 mx-auto mb-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className={`text-2xl font-bold mb-2 ${isDark ? "text-white" : "text-gray-800"}`}>
            Error Loading Resume
          </h2>
          <p className={`mb-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-md font-medium ${
              isDark
                ? "bg-blue-600 hover:bg-blue-700 text-white"
                : "bg-blue-500 hover:bg-blue-600 text-white"
            }`}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!resumeData) return null;

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen flex flex-col">
      <h1 className={`text-3xl font-bold mb-6 flex-shrink-0 ${isDark ? "text-white" : "text-gray-800"}`}>
        Resume Analysis Results
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 flex-1 min-h-0" style={{ minHeight: 'calc(100vh - 200px)' }}>
        {/* Left column: PDF Preview */}
        <div className={`rounded-lg overflow-hidden shadow-lg flex flex-col ${isDark ? "bg-dark-200" : "bg-white"}`}>
          <h2 className={`text-xl font-semibold p-4 border-b flex-shrink-0 ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
            Resume Preview
          </h2>
          <div className="flex-1 min-h-0">
            {resumeData.file_path && (
              <EnhancedPDFViewer
                filePath={resumeData.file_path}
                fileName={resumeData.title}
                className="w-full h-full"
                onError={(error) => setError(error)}
              />
            )}
          </div>
        </div>

        {/* Right column: Analysis Status and Results */}
        <div className="flex flex-col">
          {/* Analysis Status */}
          {isPolling && (
            <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
              <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
                Analysis Status
              </h2>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  {/* Status Icon */}
                  {analysisStatus?.overall_status === 'not_resume' || analysisStatus?.overall_status === 'validation_failed' ? (
                    <svg className="w-6 h-6 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : analysisStatus?.overall_status === 'completed' ? (
                    <svg className="w-6 h-6 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="animate-spin w-6 h-6 mr-3 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}

                  <span className={`text-lg font-medium ${isDark ? "text-white" : "text-gray-800"}`}>
                    {getStatusMessage()}
                  </span>
                </div>
                <div className={`w-full h-2 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"}`}>
                  <div
                    className="h-2 rounded-full bg-blue-500 transition-all duration-500 ease-out"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>
                {/* Step Progress Indicators */}
                <div className="mt-4 space-y-2">
                  {/* Step 1: Validation */}
                  <div className="flex items-center text-sm">
                    {analysisStatus?.validation_status === 'validated' ? (
                      <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : analysisStatus?.validation_status === 'validating' ? (
                      <div className="w-4 h-4 mr-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      </div>
                    ) : (
                      <div className="w-4 h-4 mr-2 border border-gray-300 rounded-full"></div>
                    )}
                    <span className={analysisStatus?.validation_status === 'validated' ? 'text-green-600' : isDark ? 'text-gray-300' : 'text-gray-600'}>
                      Document Validation
                    </span>
                  </div>

                  {/* Step 2: Content Analysis */}
                  <div className="flex items-center text-sm">
                    {analysisStatus?.parsing_status === 'parsed' ? (
                      <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : analysisStatus?.parsing_status === 'parsing' ? (
                      <div className="w-4 h-4 mr-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      </div>
                    ) : (
                      <div className="w-4 h-4 mr-2 border border-gray-300 rounded-full"></div>
                    )}
                    <span className={analysisStatus?.parsing_status === 'parsed' ? 'text-green-600' : isDark ? 'text-gray-300' : 'text-gray-600'}>
                      Content Analysis
                    </span>
                  </div>

                  {/* Step 3: Format Evaluation */}
                  <div className="flex items-center text-sm">
                    {analysisStatus?.scoring_status === 'scored' ? (
                      <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : analysisStatus?.scoring_status === 'scoring' ? (
                      <div className="w-4 h-4 mr-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      </div>
                    ) : (
                      <div className="w-4 h-4 mr-2 border border-gray-300 rounded-full"></div>
                    )}
                    <span className={analysisStatus?.scoring_status === 'scored' ? 'text-green-600' : isDark ? 'text-gray-300' : 'text-gray-600'}>
                      Format Evaluation
                    </span>
                  </div>

                  {/* Step 4: Content Analysis */}
                  <div className="flex items-center text-sm">
                    {analysisStatus?.content_analysis_status === 'completed' ? (
                      <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : analysisStatus?.content_analysis_status === 'analyzing' ? (
                      <div className="w-4 h-4 mr-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      </div>
                    ) : (
                      <div className="w-4 h-4 mr-2 border border-gray-300 rounded-full"></div>
                    )}
                    <span className={analysisStatus?.content_analysis_status === 'completed' ? 'text-green-600' : isDark ? 'text-gray-300' : 'text-gray-600'}>
                      Content Analysis
                    </span>
                  </div>
                </div>

                {/* Dynamic description based on status */}
                <p className={`mt-4 text-sm ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                  {analysisStatus?.overall_status === 'validating' &&
                    "Checking if your document is a resume and analyzing its structure..."
                  }
                  {analysisStatus?.overall_status === 'parsing' &&
                    "Document validated! Now analyzing content and extracting key information..."
                  }
                  {analysisStatus?.overall_status === 'scoring' &&
                    "Content analyzed! Evaluating format and ATS compatibility..."
                  }
                  {analysisStatus?.overall_status === 'not_resume' && analysisStatus?.validation_reason && (
                    <span className="text-orange-500">
                      {analysisStatus.validation_reason}
                    </span>
                  )}
                  {(!analysisStatus?.overall_status ||
                    !['validating', 'parsing', 'scoring', 'not_resume'].includes(analysisStatus.overall_status)) &&
                    "We're analyzing your document using advanced AI to provide comprehensive feedback on format and ATS compatibility."
                  }
                </p>
              </div>
            </div>
          )}

          {/* Claude Format Score */}
          {claudeFeedback && (
            <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
              <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
                Format & ATS Compatibility Score
              </h2>
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-lg font-medium ${isDark ? "text-white" : "text-gray-800"}`}>Format Score</span>
                  <span className={`text-2xl font-bold ${getScoreColor(claudeFeedback.claude_score)}`}>
                    {claudeFeedback.claude_score}%
                  </span>
                </div>

                {/* Progress bar */}
                <div className={`w-full h-4 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"}`}>
                  <div
                    className={`h-4 rounded-full ${
                      claudeFeedback.claude_score >= 80 ? "bg-green-500" :
                      claudeFeedback.claude_score >= 60 ? "bg-yellow-500" :
                      "bg-red-500"
                    }`}
                    style={{ width: `${claudeFeedback.claude_score}%` }}
                  ></div>
                </div>

                <p className={`mt-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                  {claudeFeedback.claude_score >= 80
                    ? "Excellent! Your resume format is well-optimized for ATS systems."
                    : claudeFeedback.claude_score >= 60
                    ? "Good format, but there are some improvements that could help with ATS compatibility."
                    : "Your resume format needs improvements to pass through ATS systems effectively."
                  }
                </p>
              </div>
            </div>
          )}

          {/* Claude Format Feedback */}
          {claudeFeedback && claudeFeedback.claude_feedback && (
            <ClaudeFeedbackSection
              claudeFeedback={claudeFeedback}
              isDark={isDark}
            />
          )}

          {/* OpenAI Content Analysis */}
          {openAIFeedback && (
            <div className={`rounded-lg overflow-hidden shadow-lg mb-6 flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
              <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
                Content Quality Score
              </h2>
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-lg font-medium ${isDark ? "text-white" : "text-gray-800"}`}>Content Score</span>
                  <span className={`text-2xl font-bold ${getScoreColor(openAIFeedback.content_quality_score)}`}>
                    {openAIFeedback.content_quality_score}%
                  </span>
                </div>

                {/* Progress bar */}
                <div className={`w-full h-4 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"}`}>
                  <div
                    className={`h-4 rounded-full ${
                      openAIFeedback.content_quality_score >= 80 ? "bg-green-500" :
                      openAIFeedback.content_quality_score >= 60 ? "bg-yellow-500" :
                      "bg-red-500"
                    }`}
                    style={{ width: `${openAIFeedback.content_quality_score}%` }}
                  ></div>
                </div>

                <p className={`mt-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                  {openAIFeedback.content_quality_score >= 80
                    ? "Excellent! Your resume content is compelling and well-structured."
                    : openAIFeedback.content_quality_score >= 60
                    ? "Good content, but there are opportunities to make it more impactful."
                    : "Your resume content needs improvements to better showcase your achievements."
                  }
                </p>
              </div>
            </div>
          )}

          {/* OpenAI Content Feedback */}
          {openAIFeedback && openAIFeedback.openai_feedback && (
            <OpenAIFeedbackSection
              openAIFeedback={openAIFeedback}
              isDark={isDark}
            />
          )}

          {/* Action Buttons */}
          <div className={`rounded-lg overflow-hidden shadow-lg flex-shrink-0 ${isDark ? "bg-dark-200" : "bg-white"}`}>
            <h2 className={`text-xl font-semibold p-4 border-b ${isDark ? "text-white border-dark-100" : "text-gray-800 border-gray-200"}`}>
              Next Steps
            </h2>
            <div className="p-6">
              <p className={`mb-4 ${isDark ? "text-gray-300" : "text-gray-600"}`}>
                {claudeFeedback
                  ? "Review the format recommendations above to improve your ATS compatibility score."
                  : "Your resume analysis is in progress. Results will appear here once complete."
                }
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="flex-1">
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    onClick={() => navigate('/resume-optimizer')}
                  >
                    Analyze Another Resume
                  </Button>
                </motion.div>

                {claudeFeedback && (
                  <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="flex-1">
                    <Button
                      variant="secondary"
                      size="lg"
                      className="w-full"
                      onClick={() => navigate('/dashboard')}
                    >
                      View All Resumes
                    </Button>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResumeResultsPage;
