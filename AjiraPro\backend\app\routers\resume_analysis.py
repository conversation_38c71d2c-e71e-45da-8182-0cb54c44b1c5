"""
Resume Analysis API Endpoints

Handles resume parsing and scoring operations
"""

import logging
from typing import Dict, Any
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse

from ..core.security import get_current_user
from ..services.supabase import supabase
from ..services.resume_validator import ai_resume_validator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/resumes", tags=["resume-analysis"])

# Add OPTIONS handler for CORS preflight for analyze endpoint
@router.options("/{resume_id}/analyze")
async def analyze_resume_options(resume_id: UUID):
    """Handle CORS preflight requests for analyze endpoint"""
    from fastapi.responses import Response

    response = Response()
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type"
    response.headers["Access-Control-Max-Age"] = "86400"

    return response

@router.post("/{resume_id}/analyze")
async def analyze_resume(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Start complete resume analysis (parsing + scoring)

    Args:
        resume_id: UUID of the resume to analyze
        current_user: Current authenticated user

    Returns:
        Analysis task information
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path, parsing_status, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]

        # Check if file exists
        if not resume_data["file_path"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )

        # Check if analysis is already in progress
        if resume_data["parsing_status"] == "parsing":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Analysis already in progress",
                    "status": "parsing",
                    "resume_id": str(resume_id)
                }
            )

        if resume_data["scoring_status"] == "scoring":
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "message": "Scoring already in progress",
                    "status": "scoring",
                    "resume_id": str(resume_id)
                }
            )

        # Start analysis using existing analyze_resume_task (which now includes validation)
        logger.info(f"🚀 Starting analysis for resume {resume_id}")

        try:
            # Import the analyze task
            from ..tasks.resume_analysis import analyze_resume_task

            # Queue the analysis task (which starts with validation)
            logger.info(f"📋 Attempting to queue analysis task for resume {resume_id}")

            task_result = analyze_resume_task.delay(str(resume_id))
            logger.info(f"✅ Analysis task queued successfully with ID: {task_result.id}")
            logger.info(f"📊 Task state: {task_result.state}")

            return JSONResponse(
                status_code=status.HTTP_202_ACCEPTED,
                content={
                    "message": "Resume analysis started successfully",
                    "resume_id": str(resume_id),
                    "task_id": task_result.id,
                    "status": "processing"
                }
            )

        except Exception as analysis_error:
            logger.error(f"Failed to start analysis for resume {resume_id}: {str(analysis_error)}")
            import traceback
            logger.error(f"Analysis error traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start analysis: {str(analysis_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start analysis: {str(e)}"
        )

@router.get("/{resume_id}/analysis-status")
async def get_resume_analysis_status(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        Current analysis status
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select("id, user_id").eq(
            "id", str(resume_id)
        ).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        # Get detailed status directly from database (including validation fields)
        resume_response = supabase.table("resumes").select(
            "validation_status, is_resume, validation_reason, parsing_status, scoring_status, validated_at, parsed_at, last_scored_at, parsing_error, scoring_error, validation_error"
        ).eq("id", str(resume_id)).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        data = resume_response.data[0]

        # Determine overall status using the updated function from tasks
        from ..tasks.resume_analysis import _determine_overall_status
        overall_status = _determine_overall_status(data)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "validation_status": data.get("validation_status"),
                "is_resume": data.get("is_resume"),
                "validation_reason": data.get("validation_reason"),
                "parsing_status": data["parsing_status"],
                "scoring_status": data["scoring_status"],
                "validated_at": data.get("validated_at"),
                "parsed_at": data["parsed_at"],
                "last_scored_at": data["last_scored_at"],
                "parsing_error": data["parsing_error"],
                "scoring_error": data["scoring_error"],
                "validation_error": data.get("validation_error"),
                "overall_status": overall_status
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis status: {str(e)}"
        )

@router.get("/{resume_id}/claude-feedback")
async def get_claude_feedback(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Get Claude format scoring feedback

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        Claude feedback and scoring data
    """
    try:
        # Verify resume belongs to user and get Claude feedback
        resume_response = supabase.table("resumes").select(
            "id, user_id, claude_score, claude_feedback, scoring_status"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]

        if resume_data["claude_score"] is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claude scoring not completed. Current status: {resume_data['scoring_status']}"
            )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "claude_score": resume_data["claude_score"],
                "claude_feedback": resume_data["claude_feedback"],
                "scoring_status": resume_data["scoring_status"]
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get Claude feedback for resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Claude feedback: {str(e)}"
        )

@router.post("/{resume_id}/validate")
async def validate_resume_ai(
    resume_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> JSONResponse:
    """
    Fast AI-based validation to check if document is a resume

    Args:
        resume_id: UUID of the resume
        current_user: Current authenticated user

    Returns:
        AI validation results
    """
    try:
        # Verify resume belongs to user
        resume_response = supabase.table("resumes").select(
            "id, user_id, file_path, title"
        ).eq("id", str(resume_id)).eq("user_id", current_user["id"]).execute()

        if not resume_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )

        resume_data = resume_response.data[0]
        file_path = resume_data["file_path"]

        if not file_path:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file associated with this resume"
            )

        # Download file from Supabase storage
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume file not found in storage"
            )

        # Validate using AI
        filename = file_path.split('/')[-1]
        validation_result = await ai_resume_validator.validate_document_as_resume(
            file_response, filename
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "resume_id": str(resume_id),
                "filename": resume_data["title"],
                "validation": validation_result
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to validate resume {resume_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )

@router.post("/test-gemini")
async def test_gemini_validation() -> JSONResponse:
    """
    Test endpoint to verify Gemini integration is working
    """
    try:
        # Test with simple text content
        test_content = """
        John Doe
        Software Engineer
        Email: <EMAIL>
        Phone: 555-1234

        EXPERIENCE
        Software Developer | Tech Corp | 2020-2023
        • Developed applications

        EDUCATION
        Bachelor of Computer Science | University | 2018

        SKILLS
        Python, JavaScript, React
        """.encode('utf-8')

        # Test Gemini validation
        validation_result = await ai_resume_validator.validate_document_as_resume(
            test_content, "test_resume.txt"
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": "Gemini validation test successful",
                "validation_result": validation_result
            }
        )

    except Exception as e:
        logger.error(f"Gemini test failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Gemini test failed: {str(e)}"
        )

@router.get("/debug/redis-queue")
async def debug_redis_queue() -> JSONResponse:
    """
    Debug endpoint to check Redis queue status
    """
    try:
        import redis
        import json
        from ..core.config import settings

        # Connect to Redis
        redis_client = redis.from_url(settings.REDIS_URL)

        # Get queue information
        queue_info = {
            "redis_connection": "connected",
            "celery_tasks": [],
            "queue_length": 0
        }

        try:
            # Check if Redis is responding
            redis_client.ping()

            # Get Celery task metadata
            keys = redis_client.keys('celery-task-meta-*')
            queue_info["queue_length"] = len(keys)

            # Get recent tasks
            for key in keys[:10]:  # Limit to 10 most recent
                task_data = redis_client.get(key)
                if task_data:
                    try:
                        task_info = json.loads(task_data)
                        queue_info["celery_tasks"].append({
                            "task_id": key.decode('utf-8').replace('celery-task-meta-', ''),
                            "status": task_info.get("status"),
                            "result": task_info.get("result")
                        })
                    except json.JSONDecodeError:
                        continue

        except Exception as redis_error:
            queue_info["redis_connection"] = f"error: {str(redis_error)}"

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=queue_info
        )

    except Exception as e:
        logger.error(f"Redis debug failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Redis debug failed: {str(e)}"
        )
