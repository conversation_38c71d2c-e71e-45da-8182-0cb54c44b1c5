"""
AI-Based Resume Validator Service.

Uses Google Gemini 1.5 Flash to quickly determine if a document is a resume
by directly processing the file without needing full text extraction.
"""

import re
import json
import logging
import base64
from typing import Dict, Any, Optional

import google.generativeai as genai
from ..core.config import settings

logger = logging.getLogger(__name__)

# File validation constants
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
VALID_FILE_TYPES = {'pdf', 'doc', 'docx', 'txt'}

class ResumeValidationError(Exception):
    """Exception raised when resume validation fails"""
    pass

class GeminiResumeValidator:
    """Service for validating resumes using Google Gemini 1.5 Flash"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = settings.GEMINI_MODEL

        # Configure Gemini API
        if settings.GEMINI_API_KEY:
            genai.configure(api_key=settings.GEMINI_API_KEY)
            self.gemini_model = genai.GenerativeModel(self.model)
        else:
            raise ResumeValidationError("GEMINI_API_KEY not configured")

    async def validate_document_as_resume(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Use Gemini 1.5 Flash to quickly determine if a document is a resume

        Args:
            file_content: Raw file content as bytes
            filename: Original filename

        Returns:
            Dict with validation results

        Raises:
            ResumeValidationError: If validation fails
        """
        # Input validation
        validation_error = self._validate_input(file_content, filename)
        if validation_error:
            return validation_error

        try:
            file_base64 = base64.b64encode(file_content).decode('utf-8')
            file_extension = filename.lower().split('.')[-1]

            # Call Gemini with enhanced prompt
            response = self.gemini_model.generate_content(
                contents=[{"text": self._create_validation_prompt(file_base64, filename, file_extension)}],
                generation_config={
                    "response_mime_type": "application/json",
                    "temperature": 0.0,  # Maximize determinism
                    "max_output_tokens": 150
                }
            )

            return self._process_response(response)

        except Exception as e:
            self.logger.exception(f"Gemini validation failed: {str(e)}")
            return self._fallback_response(str(e))

    def _validate_input(self, file_content: bytes, filename: str) -> Optional[Dict[str, Any]]:
        """Pre-flight input checks"""
        # Size checks
        if len(file_content) == 0:
            return self._rejection_response("Empty file", "high")
        if len(file_content) < 100:
            return self._rejection_response("File too small", "high")
        if len(file_content) > MAX_FILE_SIZE:
            return self._rejection_response("File too large", "high")

        # File type check
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
        if file_extension not in VALID_FILE_TYPES:
            return self._rejection_response(f"Invalid file type: {file_extension}", "high")

        return None

    def _create_validation_prompt(self, file_base64: str, filename: str, file_extension: str) -> str:
        """Create optimized validation prompt"""
        return f"""
**Objective**: Strictly determine if this document is a professional resume/CV.
Reject non-resumes including: cover letters, job descriptions, forms, invoices, reports, or fictional profiles.

**Validation Criteria**:
1. PERSONAL DETAILS: Full name, contact info (email/phone), location
2. WORK HISTORY: Company names, job titles, employment dates (month/year)
3. EDUCATION: Institutions, degrees, graduation dates
4. SKILLS: Technical/hard skills section
5. STRUCTURE: Resume-like formatting (sections, bullet points)
6. CONTENT FOCUS: Individual's professional background (not company info)

**Scoring**:
- +20 points for each present core section (work, education, skills)
- +10 for personal details
- +5 for certifications/projects
- -30 if contains company descriptions/job requirements
- -50 if structured as letter/essay

**Response Rules**:
1. Output ONLY valid JSON
2. "likelihood": 0-100 score (resume probability)
3. "is_resume": true ONLY if score ≥70
4. "confidence": "high" (≥85), "medium" (70-84), "low" (<70)
5. "reason": Concise explanation (max 80 chars)

**File Metadata**:
- FILENAME: {filename}
- TYPE: {file_extension.upper()}

**Document Data (base64)**:
{file_base64}
"""

    def _process_response(self, response) -> Dict[str, Any]:
        """Process and sanitize Gemini response"""
        if not response or not response.text:
            raise ResumeValidationError("Empty Gemini response")

        # Sanitize response
        sanitized = re.sub(r'[\s\W_]+', '', response.text.strip())
        try:
            result = json.loads(sanitized)
        except json.JSONDecodeError:
            # Attempt to extract JSON substring
            json_match = re.search(r'\{.*\}', response.text)
            if json_match:
                result = json.loads(json_match.group())
            else:
                raise ResumeValidationError("Invalid JSON format")

        # Validate response structure
        required_keys = {"is_resume", "likelihood", "reason", "confidence"}
        if not all(key in result for key in required_keys):
            raise ResumeValidationError("Missing keys in response")

        # Normalize values
        likelihood = max(0, min(100, int(result.get("likelihood", 0))))
        is_resume = likelihood >= 70

        # Confidence mapping
        confidence = "low"
        if likelihood >= 85:
            confidence = "high"
        elif likelihood >= 70:
            confidence = "medium"

        # Reason sanitization
        reason = result.get("reason", "")[:80]

        return {
            "is_resume": is_resume,
            "likelihood": likelihood,
            "reason": reason,
            "confidence": confidence,
            "validation_method": "gemini_1.5_flash"
        }

    def _rejection_response(self, reason: str, confidence: str) -> Dict[str, Any]:
        """Standard rejection format"""
        return {
            "is_resume": False,
            "likelihood": 0,
            "reason": reason,
            "confidence": confidence,
            "validation_method": "pre_validation"
        }

    def _fallback_response(self, error: str) -> Dict[str, Any]:
        """Fallback for Gemini failures"""
        return {
            "is_resume": False,
            "likelihood": 0,
            "reason": f"Validation error: {error[:60]}",
            "confidence": "low",
            "validation_method": "error_fallback"
        }

# Global Gemini resume validator instance (lazy-loaded)
_ai_resume_validator = None

def get_ai_resume_validator() -> GeminiResumeValidator:
    """Dependency injection for Gemini resume validator (lazy-loaded)"""
    global _ai_resume_validator
    if _ai_resume_validator is None:
        _ai_resume_validator = GeminiResumeValidator()
    return _ai_resume_validator

# For backward compatibility
ai_resume_validator = get_ai_resume_validator()
