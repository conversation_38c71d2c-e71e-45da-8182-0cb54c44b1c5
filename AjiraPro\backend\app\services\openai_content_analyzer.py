"""
OpenAI Content Analysis Service

This service handles content quality analysis of resumes using OpenAI GPT models.
It evaluates experience descriptions, education details, skills presentation,
and overall content quality to provide actionable improvement suggestions.
"""

import json
import logging
from typing import Dict, Any, List
from datetime import datetime

from .ai import AIService, AIServiceError

logger = logging.getLogger(__name__)

class OpenAIContentAnalysisError(Exception):
    """Exception raised when OpenAI content analysis fails"""
    pass

class OpenAIContentAnalyzer:
    """OpenAI-powered content quality analyzer for resumes"""
    
    def __init__(self, model: str = "gpt-4"):
        self.model = model
        self.ai_service = AIService()
        self.logger = logging.getLogger(__name__)
    
    async def analyze_content_quality(self, parsed_content: Dict[str, Any]) -> tuple[float, Dict[str, Any]]:
        """
        Analyze resume content quality using OpenAI
        
        Args:
            parsed_content: Structured resume data from <PERSON> parsing
            
        Returns:
            Tuple of (content_score, detailed_feedback)
            
        Raises:
            OpenAIContentAnalysisError: If analysis fails
        """
        try:
            analysis_prompt = self._create_content_analysis_prompt(parsed_content)
            
            messages = [
                {
                    "role": "system",
                    "content": """You are an expert career coach and resume content analyst. 
                    Analyze resume content for quality, impact, clarity, and completeness.
                    Focus on content substance, not formatting. Provide specific, actionable feedback.
                    Always respond with valid JSON only."""
                },
                {
                    "role": "user",
                    "content": analysis_prompt
                }
            ]
            
            response = await self.ai_service.call_openai(messages, model=self.model)
            
            # Extract content from OpenAI response
            if "choices" in response and len(response["choices"]) > 0:
                content_result = response["choices"][0]["message"]["content"]
            else:
                raise OpenAIContentAnalysisError("Invalid response format from OpenAI")
            
            # Parse JSON response
            try:
                analysis_data = json.loads(content_result)
                score = float(analysis_data.get("overall_content_score", 0))
                feedback = self._structure_openai_feedback(analysis_data)
                return score, feedback
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.error(f"Failed to parse OpenAI content analysis response: {str(e)}")
                raise OpenAIContentAnalysisError(f"Invalid analysis response from OpenAI: {str(e)}")
                
        except AIServiceError as e:
            self.logger.error(f"OpenAI API error during content analysis: {str(e)}")
            raise OpenAIContentAnalysisError(f"OpenAI API error: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error during content analysis: {str(e)}")
            raise OpenAIContentAnalysisError(f"Content analysis failed: {str(e)}")
    
    def _create_content_analysis_prompt(self, parsed_content: Dict[str, Any]) -> str:
        """Create prompt for OpenAI content analysis"""
        
        return f"""
Analyze the content quality of this resume and provide detailed feedback on how to improve it.

Resume Content:
{json.dumps(parsed_content, indent=2)}

Evaluate these key areas:
1. **Professional Summary**: Clarity, impact, specificity, value proposition
2. **Work Experience**: Achievement quantification, action verbs, relevance, progression
3. **Education**: Completeness, relevance, additional qualifications
4. **Skills**: Organization, relevance, specificity, technical vs soft skills
5. **Overall Content**: Consistency, completeness, professional tone, keyword optimization

For each issue found, provide:
- Specific problem description
- Severity level (high/medium/low)
- Actionable improvement recommendation
- Section affected
- Current text example (if applicable)
- Suggested improvement (if applicable)

**CRITICAL SCORING INSTRUCTIONS:**
- Calculate a unique, precise score for THIS specific resume based on actual content quality
- Use decimal precision (e.g., 67.3, 84.7, 91.2) - avoid round numbers like 70, 75, 80
- Score range: 0-100 where 90+ = exceptional, 80-89 = strong, 70-79 = good, 60-69 = needs improvement, <60 = significant issues
- Each section score should reflect the actual quality found in that section
- Overall score should be calculated based on weighted average of section scores

Respond with this JSON format:
{{
  "overall_content_score": [CALCULATE_PRECISE_SCORE_BASED_ON_ACTUAL_CONTENT],
  "content_analysis": {{
    "professional_summary": {{
      "score": [ACTUAL_SCORE_FOR_THIS_SECTION],
      "issues": ["List specific issues found"],
      "recommendations": ["Specific actionable improvements"]
    }},
    "work_experience": {{
      "score": [ACTUAL_SCORE_FOR_THIS_SECTION],
      "issues": ["List specific issues found"],
      "recommendations": ["Specific actionable improvements"]
    }},
    "education": {{
      "score": [ACTUAL_SCORE_FOR_THIS_SECTION],
      "issues": ["List specific issues found"],
      "recommendations": ["Specific actionable improvements"]
    }},
    "skills": {{
      "score": [ACTUAL_SCORE_FOR_THIS_SECTION],
      "issues": ["List specific issues found"],
      "recommendations": ["Specific actionable improvements"]
    }},
    "content_quality": {{
      "score": [ACTUAL_SCORE_FOR_THIS_SECTION],
      "issues": ["List specific issues found"],
      "recommendations": ["Specific actionable improvements"]
    }}
  }},
  "detailed_feedback": [
    {{
      "issue": "Specific issue description",
      "severity": "high|medium|low",
      "recommendation": "Specific actionable recommendation",
      "section": "section_name",
      "current_text": "Actual text from resume if applicable",
      "suggested_improvement": "Specific improvement suggestion"
    }}
  ]
}}
"""
    
    def _structure_openai_feedback(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Structure OpenAI feedback into standardized format"""
        content_issues = []
        
        # Extract issues from detailed feedback
        if "detailed_feedback" in analysis_data:
            for item in analysis_data["detailed_feedback"]:
                content_issues.append({
                    "issue": item.get("issue", ""),
                    "severity": item.get("severity", "medium"),
                    "recommendation": item.get("recommendation", ""),
                    "section": item.get("section", ""),
                    "current_text": item.get("current_text", ""),
                    "suggested_improvement": item.get("suggested_improvement", "")
                })
        
        # Extract content analysis issues
        content_analysis = analysis_data.get("content_analysis", {})
        for category, analysis in content_analysis.items():
            if isinstance(analysis, dict) and analysis.get("issues"):
                for issue in analysis["issues"]:
                    content_issues.append({
                        "issue": issue,
                        "severity": "medium",
                        "recommendation": analysis.get("recommendations", [""])[0] if analysis.get("recommendations") else "",
                        "section": category,
                        "current_text": "",
                        "suggested_improvement": ""
                    })
        
        return {
            "content_issues": content_issues,
            "content_analysis": content_analysis,
            "overall_analysis": analysis_data
        }
