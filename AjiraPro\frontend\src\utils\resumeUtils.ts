import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

// API base URL - use environment variable or fallback
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://fajirapro-production-6ba6.up.railway.app';

/**
 * Upload a resume file to Supabase storage and create a resume record in the database
 * @param file The resume file to upload
 * @param userId The ID of the user who owns the resume
 * @returns The ID of the created resume
 */
export const uploadResume = async (file: File, userId: string): Promise<string> => {
  try {
    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    // 1. Upload the file to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('resumes')
      .upload(filePath, file);

    if (uploadError) {
      throw new Error(`Error uploading file: ${uploadError.message}`);
    }

    // 2. Create a record in the resumes table
    const { data: resumeData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: userId,
        title: file.name, // Use the original filename as the title
        status: 'pending_analysis',
        file_path: filePath
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(`Error creating resume record: ${insertError.message}`);
    }

    return resumeData.id;
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Get a resume by ID
 * @param resumeId The ID of the resume to get
 * @returns The resume data
 */
export const getResume = async (resumeId: string) => {
  try {
    const { data, error } = await supabase
      .from('resumes')
      .select('*')
      .eq('id', resumeId)
      .single();

    if (error) {
      throw new Error(`Error fetching resume: ${error.message}`);
    }

    return data;
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Update a resume's ATS score
 * @param resumeId The ID of the resume to update
 * @param atsScore The ATS score to set
 */
export const updateResumeAtsScore = async (resumeId: string, atsScore: number) => {
  try {
    const { error } = await supabase
      .from('resumes')
      .update({ ats_score: atsScore })
      .eq('id', resumeId);

    if (error) {
      throw new Error(`Error updating resume ATS score: ${error.message}`);
    }
  } catch (error) {
    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Get the URL for a resume file
 * @param filePath The path of the file in Supabase storage
 * @returns The URL of the file
 */
export const getResumeFileUrl = async (filePath: string) => {
  try {
    // First try to get a signed URL with a longer expiry
    const { data, error } = await supabase.storage
      .from('resumes')
      .createSignedUrl(filePath, 60 * 60 * 24); // 24 hour expiry for better caching

    if (error) {
      throw new Error(`Error creating signed URL: ${error.message}`);
    }

    if (!data || !data.signedUrl) {
      throw new Error('No signed URL returned from Supabase');
    }

    // Add cache control parameters to the URL
    const url = new URL(data.signedUrl);
    url.searchParams.append('cache-control', 'no-cache');

    return url.toString();
  } catch (error: any) {
    // Try fallback to public URL if signed URL fails
    try {
      const publicUrl = supabase.storage.from('resumes').getPublicUrl(filePath).data.publicUrl;

      if (publicUrl) {
        return publicUrl;
      }
    } catch (fallbackError) {
      // Fallback failed
    }

    // Error will be handled by the calling function
    throw error;
  }
};

/**
 * Start Claude analysis for a resume
 * @param resumeId The ID of the resume to analyze
 * @returns Analysis task information
 */
export const startResumeAnalysis = async (resumeId: string) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('🚀 Starting resume analysis for ID:', resumeId);
    }

    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ User not authenticated - no access token');
      }
      throw new Error('User not authenticated');
    }

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('✅ User authenticated, making API call to:', `${API_BASE_URL}/api/resumes/${resumeId}/analyze`);
    }

    const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}/analyze`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('📡 API Response Status:', response.status, response.statusText);
    }

    if (!response.ok) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ API call failed with status:', response.status);
      }
      const errorData = await response.json().catch(() => ({}));
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ Error response data:', errorData);
      }

      // Handle different error response formats
      let errorMessage = `HTTP error! status: ${response.status}`;

      if (errorData.detail) {
        // If detail is a string, use it directly
        if (typeof errorData.detail === 'string') {
          errorMessage = errorData.detail;
        } else {
          // If detail is an object, try to extract meaningful information
          errorMessage = JSON.stringify(errorData.detail);
        }
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.error) {
        errorMessage = errorData.error;
      }

      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ Final error message:', errorMessage);
      }
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('✅ Analysis started successfully:', responseData);
    }
    return responseData;
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('❌ startResumeAnalysis failed:', error.message);
    }
    throw new Error(`Failed to start analysis: ${error.message}`);
  }
};

/**
 * Get analysis status for a resume
 * @param resumeId The ID of the resume
 * @returns Current analysis status
 */
export const getAnalysisStatus = async (resumeId: string) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('🔍 Checking analysis status for resume:', resumeId);
    }

    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ User not authenticated - no access token');
      }
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}/analysis-status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('📡 Status check response:', response.status, response.statusText);
    }

    if (!response.ok) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ Status check failed with status:', response.status);
      }
      const errorData = await response.json().catch(() => ({}));
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('❌ Status error data:', errorData);
      }

      // Handle different error response formats
      let errorMessage = `HTTP error! status: ${response.status}`;

      if (errorData.detail) {
        errorMessage = typeof errorData.detail === 'string' ? errorData.detail : JSON.stringify(errorData.detail);
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.error) {
        errorMessage = errorData.error;
      }

      throw new Error(errorMessage);
    }

    const statusData = await response.json();
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('📊 Current analysis status:', statusData);
    }
    return statusData;
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('❌ getAnalysisStatus failed:', error.message);
    }
    throw new Error(`Failed to get analysis status: ${error.message}`);
  }
};

/**
 * Get parsed content for a resume
 * @param resumeId The ID of the resume
 * @returns Parsed resume content
 */
export const getParsedContent = async (resumeId: string) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}/parsed-content`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle different error response formats
      let errorMessage = `HTTP error! status: ${response.status}`;

      if (errorData.detail) {
        errorMessage = typeof errorData.detail === 'string' ? errorData.detail : JSON.stringify(errorData.detail);
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.error) {
        errorMessage = errorData.error;
      }

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error: any) {
    throw new Error(`Failed to get parsed content: ${error.message}`);
  }
};

/**
 * Get Claude feedback for a resume
 * @param resumeId The ID of the resume
 * @returns Claude feedback and scoring data
 */
export const getClaudeFeedback = async (resumeId: string) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}/claude-feedback`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to get Claude feedback');
    }

    return await response.json();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error getting Claude feedback:', error);
    throw error;
  }
};

/**
 * Get OpenAI content feedback for a resume
 * @param resumeId The ID of the resume
 * @returns OpenAI content feedback and scoring data
 */
export const getOpenAIFeedback = async (resumeId: string) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}/openai-feedback`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle different error response formats
      let errorMessage = `HTTP error! status: ${response.status}`;

      if (errorData.detail) {
        errorMessage = typeof errorData.detail === 'string' ? errorData.detail : JSON.stringify(errorData.detail);
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.error) {
        errorMessage = errorData.error;
      }

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error: any) {
    throw new Error(`Failed to get OpenAI feedback: ${error.message}`);
  }
};