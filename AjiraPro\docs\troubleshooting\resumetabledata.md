id
uuid
6aa220fb-2422-4d85-a426-b1169de65d76
user_id
uuid
f931c2e9-db27-4151-9c04-21ec6a8e4e50

Has a foreign key relation topublic.profiles.id
title
text
Ansh-resume.pdf

status
text
pending_analysis

created_at
timestamptz

06/06/2025 10:49:32 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

updated_at
timestamptz

06/06/2025 10:49:32 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

Optional Fields
These are columns that do not need any value

file_path
text
f931c2e9-db27-4151-9c04-21ec6a8e4e50/e7b1d388-a371-4ee8-8c31-58ac89cc316a.pdf

ats_score
int4
NULL
parsed_content
jsonb
"{\"personal_info\": {\"name\": \"ANSH PANDEY\", \"email\": \"<EMAIL>\", \"phone\": \"+91-9993165651\", \"location\": null, \"linkedin\": \"linkedin.com/in/ansh-pandeyy\", \"github\": \"github.com/anshhpandey\", \"website\": null}, \"professional_summary\": null, \"experience\": [{\"title\": \"ReactJS developer (Intern)\", \"company\": \"Eskills Web Indore\", \"location\": \"Indore\", \"start_date\": \"2023-02\", \"end_date\": \"2023-07\", \"description\": \"Contributed to front-end development using React.js, assisting in building responsive and user-friendly web applications.\", \"achievements\": [\"Gained proficiency in coding, testing, and debugging while following best practices and coding standards\", \"Documented code changes and project progress during the internship\", \"Collaborated with a team of experienced developers, designers, and project managers to implement new features\", \"Worked closely with the UI/UX design team, translating design mockups and wireframes into functional user interfaces\", \"Demonstrated adaptability and a strong commitment to contributing to real-world projects\"], \"technologies\": [\"React.js\"]}], \"education\": [{\"degree\": \"MCA\", \"field\": null, \"institution\": \"Shri RGP Gujarati Professional Institute Indore\", \"location\": \"Indore\", \"graduation_date\": \"2024\", \"gpa\": \"7.0\", \"honors\": []}, {\"degree\": \"B.Sc\", \"field\": \"Computer Science\", \"institution\": \"SBN Govt. PG College Barwani\", \"location\": \"Barwani\", \"graduation_date\": \"2021\", \"gpa\": \"70%\", \"honors\": []}, {\"degree\": \"Class XII\", \"field\": \"PCM\", \"institution\": \"Kendriya Vidyalaya Barwani\", \"location\": \"Barwani\", \"graduation_date\": \"2018\", \"gpa\": \"59%\", \"honors\": []}, {\"degree\": \"Class X\", \"field\": null, \"institution\": \"Kendriya Vidyalaya Barwani\", \"location\": \"Barwani\", \"graduation_date\": \"2016\", \"gpa\": \"9.0\", \"honors\": []}], \"skills\": {\"technical\": [\"HTML\", \"CSS\", \"Bootstrap\", \"Javascript\", \"ReactJS\", \"Redux\"], \"soft\": [], \"tools\": [], \"languages\": []}, \"certifications\": [], \"projects\": [{\"name\": \"E-Commerce Application\", \"description\": \"Developed a responsive frontend Ecommerce website using React.js and leveraged Redux for efficient state management.\", \"technologies\": [\"React.js\", \"Redux\"], \"url\": null, \"start_date\": null, \"end_date\": null}, {\"name\": \"Weather Application\", \"description\": \"Utilized third-party weather APIs to fetch and display weather data for various locations.\", \"technologies\": [\"React\"], \"url\": null, \"start_date\": null, \"end_date\": null}, {\"name\": \"Youtube Clone\", \"description\": \"Designing an intuitive and dynamic homepage that displays a curated list of trending videos and personalized video recommendations based on user preferences.\", \"technologies\": [], \"url\": null, \"start_date\": null, \"end_date\": null}], \"additional_sections\": {\"volunteer_work\": [], \"publications\": [], \"awards\": [\"Participated in the State Level Chess Championship, 2017\", \"Participated in the State-Level Football Championship, 2019\"], \"languages\": [], \"interests\": []}}"

Edit
Structured JSON content extracted from resume by Claude

parsing_status
text
parsed

Status of resume parsing: pending, parsing, parsed, failed

parsed_at
timestamptz

06/06/2025 10:49:55 PM

Timestamp when resume was successfully parsed

Your local timezone will be automatically applied (+0300)

parsing_error
text
NULL

Error message if parsing failed

overall_score
numeric
NULL
Overall resume score (0-100) calculated from Claude and OpenAI scores

claude_score
numeric
72.50
Format and structure score from Claude (25% of overall score)

openai_score
numeric
NULL
Content score from OpenAI (75% of overall score)

career_overview_score
numeric
NULL
Career overview section score (25% of OpenAI score)

experience_score
numeric
NULL
Experience section score (40% of OpenAI score)

education_score
numeric
NULL
Education section score (15% of OpenAI score)

additional_qualifications_score
numeric
NULL
Additional qualifications score (10% of OpenAI score)

content_quality_score
numeric
NULL
Content quality score (10% of OpenAI score)

scoring_feedback
jsonb
NULL

Edit
Detailed feedback and suggestions from AI analysis

scoring_status
text
scored

Status of resume scoring: pending, scoring, scored, failed

last_scored_at
timestamptz

06/06/2025 10:50:07 PM

Timestamp of the last scoring analysis

Your local timezone will be automatically applied (+0300)

scoring_error
text
NULL

Error message if scoring failed

claude_feedback
jsonb
"{\"format_issues\": [{\"issue\": \"Words running together without spaces\", \"severity\": \"high\", \"recommendation\": \"Add proper spacing between all words and ensure consistent formatting\", \"section\": \"all_sections\"}, {\"issue\": \"Inconsistent date formatting\", \"severity\": \"medium\", \"recommendation\": \"Use consistent MM/YYYY format throughout the resume\", \"section\": \"education_and_experience\"}, {\"issue\": \"Overly long bullet points\", \"severity\": \"medium\", \"recommendation\": \"Break down long bullet points into shorter, more focused statements\", \"section\": \"experience\"}, {\"issue\": \"Inconsistent spacing between sections\", \"severity\": \"medium\", \"recommendation\": \"Maintain uniform spacing between all sections\", \"section\": \"document_structure\"}, {\"issue\": \"Education and experience sections properly ordered chronologically\", \"severity\": \"medium\", \"recommendation\": \"\", \"section\": \"chronological_order\"}, {\"issue\": \"Some section headings lack spacing between words\", \"severity\": \"medium\", \"recommendation\": \"Add proper spacing in section headings like 'Technical Skills' and 'Work Experience'\", \"section\": \"section_headings\"}, {\"issue\": \"Words are running together without spaces\", \"severity\": \"medium\", \"recommendation\": \"Add proper spacing between words\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Inconsistent spacing throughout document\", \"severity\": \"medium\", \"recommendation\": \"Add proper spacing between words\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Some lines exceed recommended length\", \"severity\": \"medium\", \"recommendation\": \"Add proper spacing between words\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Mixed date formats (Feb'23 vs 2022-2024)\", \"severity\": \"medium\", \"recommendation\": \"Standardize all dates to MM/YYYY format\", \"section\": \"date_consistency\"}, {\"issue\": \"Contact information uses pipes (|) as separators\", \"severity\": \"medium\", \"recommendation\": \"Use standard bullet points or commas to separate contact information\", \"section\": \"contact_information\"}, {\"issue\": \"Multiple formatting inconsistencies\", \"severity\": \"medium\", \"recommendation\": \"Maintain consistent spacing between sections\", \"section\": \"formatting_quality\"}, {\"issue\": \"Some bullet points exceed 2 lines\", \"severity\": \"medium\", \"recommendation\": \"Maintain consistent spacing between sections\", \"section\": \"formatting_quality\"}, {\"issue\": \"Inconsistent spacing between sections\", \"severity\": \"medium\", \"recommendation\": \"Maintain consistent spacing between sections\", \"section\": \"formatting_quality\"}], \"ats_compatibility\": {\"score\": 72.5, \"analysis\": {\"chronological_order\": {\"score\": 90, \"issues\": [\"Education and experience sections properly ordered chronologically\"], \"recommendations\": []}, \"section_headings\": {\"score\": 85, \"issues\": [\"Some section headings lack spacing between words\"], \"recommendations\": [\"Add proper spacing in section headings like 'Technical Skills' and 'Work Experience'\"]}, \"ats_compatibility\": {\"score\": 65, \"issues\": [\"Words are running together without spaces\", \"Inconsistent spacing throughout document\", \"Some lines exceed recommended length\"], \"recommendations\": [\"Add proper spacing between words\", \"Break long lines into shorter bullet points\", \"Remove special characters and ensure consistent spacing\"]}, \"date_consistency\": {\"score\": 70, \"issues\": [\"Mixed date formats (Feb'23 vs 2022-2024)\"], \"recommendations\": [\"Standardize all dates to MM/YYYY format\"]}, \"contact_information\": {\"score\": 80, \"issues\": [\"Contact information uses pipes (|) as separators\"], \"recommendations\": [\"Use standard bullet points or commas to separate contact information\"]}, \"formatting_quality\": {\"score\": 45, \"issues\": [\"Multiple formatting inconsistencies\", \"Some bullet points exceed 2 lines\", \"Inconsistent spacing between sections\"], \"recommendations\": [\"Maintain consistent spacing between sections\", \"Limit bullet points to 2 lines maximum\", \"Use consistent formatting throughout\"]}}}, \"overall_analysis\": {\"overall_score\": 72.5, \"format_analysis\": {\"chronological_order\": {\"score\": 90, \"issues\": [\"Education and experience sections properly ordered chronologically\"], \"recommendations\": []}, \"section_headings\": {\"score\": 85, \"issues\": [\"Some section headings lack spacing between words\"], \"recommendations\": [\"Add proper spacing in section headings like 'Technical Skills' and 'Work Experience'\"]}, \"ats_compatibility\": {\"score\": 65, \"issues\": [\"Words are running together without spaces\", \"Inconsistent spacing throughout document\", \"Some lines exceed recommended length\"], \"recommendations\": [\"Add proper spacing between words\", \"Break long lines into shorter bullet points\", \"Remove special characters and ensure consistent spacing\"]}, \"date_consistency\": {\"score\": 70, \"issues\": [\"Mixed date formats (Feb'23 vs 2022-2024)\"], \"recommendations\": [\"Standardize all dates to MM/YYYY format\"]}, \"contact_information\": {\"score\": 80, \"issues\": [\"Contact information uses pipes (|) as separators\"], \"recommendations\": [\"Use standard bullet points or commas to separate contact information\"]}, \"formatting_quality\": {\"score\": 45, \"issues\": [\"Multiple formatting inconsistencies\", \"Some bullet points exceed 2 lines\", \"Inconsistent spacing between sections\"], \"recommendations\": [\"Maintain consistent spacing between sections\", \"Limit bullet points to 2 lines maximum\", \"Use consistent formatting throughout\"]}}, \"detailed_feedback\": [{\"issue\": \"Words running together without spaces\", \"severity\": \"high\", \"recommendation\": \"Add proper spacing between all words and ensure consistent formatting\", \"section\": \"all_sections\"}, {\"issue\": \"Inconsistent date formatting\", \"severity\": \"medium\", \"recommendation\": \"Use consistent MM/YYYY format throughout the resume\", \"section\": \"education_and_experience\"}, {\"issue\": \"Overly long bullet points\", \"severity\": \"medium\", \"recommendation\": \"Break down long bullet points into shorter, more focused statements\", \"section\": \"experience\"}, {\"issue\": \"Inconsistent spacing between sections\", \"severity\": \"medium\", \"recommendation\": \"Maintain uniform spacing between all sections\", \"section\": \"document_structure\"}]}}"

Edit
openai_feedback
jsonb
NULL

Edit
validation_status
varchar
validated

Gemini validation status: pending, validating, completed, failed

is_resume
bool

TRUE

TRUE
Gemini validation result: true if document is a resume, false if not

validation_likelihood
int4
90
Gemini confidence percentage (0-100)

validation_reason
text
Fallback validation - Gemini validation failed
Gemini explanation for validation result
validation_error
text
'coroutine' object is not subscriptable
Error message if validation failed
validated_at
timestamp
06/06/2025 07:49:37 PM
Timestamp when validation completed
