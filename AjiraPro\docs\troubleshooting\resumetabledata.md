resumes
id
uuid
c8673db5-9e79-4a18-80b5-4dc7e82d79a8
user_id
uuid
f931c2e9-db27-4151-9c04-21ec6a8e4e50

Has a foreign key relation topublic.profiles.id
title
text
Teresa resume.pdf

status
text
pending_analysis

created_at
timestamptz

06/09/2025 12:51:07 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

updated_at
timestamptz

06/09/2025 12:51:07 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

Optional Fields
These are columns that do not need any value

file_path
text
f931c2e9-db27-4151-9c04-21ec6a8e4e50/dbb7591a-40ac-427e-a685-6afbeb3b81c1.pdf

ats_score
int4
NULL
parsed_content
jsonb
"{\"personal_info\": {\"name\": \"Teresa Technical\", \"email\": \"<EMAIL>\", \"phone\": \"(*************\", \"location\": \"Austin, TX\", \"linkedin\": \"Linkedin\", \"github\": \"github\", \"website\": null}, \"professional_summary\": null, \"experience\": [{\"title\": \"Software Development Intern\", \"company\": \"Pros\", \"location\": \"Denver, CO\", \"start_date\": \"2023-06\", \"end_date\": \"2021-08\", \"description\": null, \"achievements\": [\"Designed a REST API call in Java that allows clients to simultaneously update multiple interdependent elements within a large SQL Server database, resulting in significant performance increases for both client and server\", \"Used a topological sorting algorithm on a DAG in order to correctly resolve data dependencies\", \"Refactored existing API calls to use XML Serializable objects and new API backend methods\"], \"technologies\": [\"Java\", \"SQL Server\", \"REST API\", \"XML\"]}, {\"title\": \"Online Marketing Intern\", \"company\": \"Indeed\", \"location\": \"Austin, TX\", \"start_date\": \"2022-05\", \"end_date\": \"2020-08\", \"description\": null, \"achievements\": [\"Designed Web Application from scratch using Django and MySQL that allows Indeed's employees to query, compare and store various marketing statistics across different groups of companies for purpose of A/B testing\", \"Used a Na\\u00efve Bayes classifier to determine the significance of each metric and displayed custom graphs corresponding to the most relevant metrics of each query\", \"Implemented an LDAP system to allow users to log in and save custom charts, graphs, information, and queries\"], \"technologies\": [\"Django\", \"MySQL\", \"LDAP\"]}, {\"title\": \"Undergraduate Research Assistant\", \"company\": \"Computational Materials Stream, Freshman Research Initiative, UT Austin\", \"location\": \"Austin, TX\", \"start_date\": \"2020-01\", \"end_date\": \"2021-05\", \"description\": null, \"achievements\": [\"Obtained skills for research in computational science and engineering with applications in materials and chemistry research to find new catalytic materials for alternative energy technologies and improve prediction of catalytic behavior\", \"Presented poster of final research project on the performance of Palladium-Technetium catalysts in fuel cells\"], \"technologies\": []}], \"education\": [{\"degree\": \"Bachelor of Science\", \"field\": \"Computer Science\", \"institution\": \"The University of Texas at Austin\", \"location\": \"Austin, TX\", \"graduation_date\": \"2024-05\", \"gpa\": \"3.3\", \"honors\": null}], \"skills\": {\"technical\": [\"Java\", \"C\", \"C++\", \"SQL\", \"Python\", \"Javascript\"], \"soft\": null, \"tools\": null, \"languages\": null}, \"certifications\": [], \"projects\": [{\"name\": \"Twitlist\", \"description\": \"Created a Spotify playlist from the hashtags used by a given twitter account\", \"technologies\": [\"Flask\", \"Mako\", \"Requests\", \"Spotify API\", \"Twitter API\"], \"url\": null, \"start_date\": null, \"end_date\": null}, {\"name\": \"APEX Applications\", \"description\": \"Using combined knowledge of JSON, SQL, and Cypher, made online applications that exercised permissions and a mobile application that fetches radio IP streams\", \"technologies\": [\"JSON\", \"SQL\", \"Cypher\"], \"url\": null, \"start_date\": null, \"end_date\": null}, {\"name\": \"Compiler\", \"description\": \"Built compiler for a script language interpreting basic arithmetic and Boolean operations\", \"technologies\": null, \"url\": null, \"start_date\": null, \"end_date\": null}], \"additional_sections\": {\"volunteer_work\": [{\"organization\": \"Code Orange\", \"role\": \"Curriculum Director, Treasurer\", \"location\": \"The University of Texas at Austin, Austin, TX\", \"start_date\": \"2021-08\", \"end_date\": \"Present\", \"description\": [\"Help keep 100+ low income neighborhood elementary school children engaged by keeping curriculum fun and challenging\", \"Expanded curriculum to include more hands-on learning (Ozobots, Circuit Builder, Code.org, Scratch, Jeroo)\", \"Budget for the academic year to maximize educational and experiential benefits to 100+ mentees\", \"Manage interactions with the UT Computer Science department\"]}], \"publications\": [], \"awards\": [], \"languages\": [], \"interests\": []}}"

Edit
Structured JSON content extracted from resume by Claude

parsing_status
text
parsed

Status of resume parsing: pending, parsing, parsed, failed

parsed_at
timestamptz

06/09/2025 12:51:33 PM

Timestamp when resume was successfully parsed

Your local timezone will be automatically applied (+0300)

parsing_error
text
NULL

Error message if parsing failed

overall_score
numeric
NULL
Overall resume score (0-100) calculated from Claude and OpenAI scores

claude_score
numeric
82.50
Format and structure score from Claude (25% of overall score)

openai_score
numeric
NULL
Content score from OpenAI (75% of overall score)

career_overview_score
numeric
NULL
Career overview section score (25% of OpenAI score)

experience_score
numeric
NULL
Experience section score (40% of OpenAI score)

education_score
numeric
NULL
Education section score (15% of OpenAI score)

additional_qualifications_score
numeric
NULL
Additional qualifications score (10% of OpenAI score)

content_quality_score
numeric
70.00
Content quality score (10% of OpenAI score)

scoring_feedback
jsonb
NULL

Edit
Detailed feedback and suggestions from AI analysis

scoring_status
text
scored

Status of resume scoring: pending, scoring, scored, failed

last_scored_at
timestamptz

06/09/2025 12:51:44 PM

Timestamp of the last scoring analysis

Your local timezone will be automatically applied (+0300)

scoring_error
text
NULL

Error message if scoring failed

claude_feedback
jsonb
"{\"format_issues\": [{\"issue\": \"Date ranges appear reversed in experience section (e.g., June 2023 \\u2013 August 2021)\", \"severity\": \"high\", \"recommendation\": \"Correct date ranges to show earlier date first\", \"section\": \"experience\"}, {\"issue\": \"Bullet points in experience section sometimes exceed 2 lines\", \"severity\": \"medium\", \"recommendation\": \"Condense bullet points to be more concise\", \"section\": \"experience\"}, {\"issue\": \"Contact information formatting uses non-standard separators\", \"severity\": \"low\", \"recommendation\": \"Use standard separators like commas or bullets instead of pipes\", \"section\": \"personal_info\"}, {\"issue\": \"Technical skills section uses semicolons and varying proficiency descriptions\", \"severity\": \"medium\", \"recommendation\": \"Use consistent format for skills listing, preferably as bullet points\", \"section\": \"skills\"}, {\"issue\": \"Date inconsistencies in experience section\", \"severity\": \"medium\", \"recommendation\": \"Fix date order in experience section\", \"section\": \"chronological_order\"}, {\"issue\": \"Some dates appear to be reversed\", \"severity\": \"medium\", \"recommendation\": \"Fix date order in experience section\", \"section\": \"chronological_order\"}, {\"issue\": \"Pipe symbols in contact information may cause parsing issues\", \"severity\": \"medium\", \"recommendation\": \"Replace pipe symbols with spaces or bullet points in contact section\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Mixed date formats (Month YYYY and MM/YYYY)\", \"severity\": \"medium\", \"recommendation\": \"Standardize all dates to MM/YYYY format\", \"section\": \"date_consistency\"}, {\"issue\": \"Some date ranges appear reversed\", \"severity\": \"medium\", \"recommendation\": \"Standardize all dates to MM/YYYY format\", \"section\": \"date_consistency\"}, {\"issue\": \"Pipe symbols in contact line\", \"severity\": \"medium\", \"recommendation\": \"Separate contact elements with commas or bullet points instead of pipes\", \"section\": \"contact_information\"}, {\"issue\": \"Some bullet points exceed 2 lines\", \"severity\": \"medium\", \"recommendation\": \"Shorten bullet points to maximum 2 lines\", \"section\": \"formatting_quality\"}, {\"issue\": \"Inconsistent spacing between sections\", \"severity\": \"medium\", \"recommendation\": \"Shorten bullet points to maximum 2 lines\", \"section\": \"formatting_quality\"}], \"ats_compatibility\": {\"score\": 82.5, \"analysis\": {\"chronological_order\": {\"score\": 75, \"issues\": [\"Date inconsistencies in experience section\", \"Some dates appear to be reversed\"], \"recommendations\": [\"Fix date order in experience section\", \"Ensure all dates follow MM/YYYY format consistently\"]}, \"section_headings\": {\"score\": 90, \"issues\": [], \"recommendations\": [\"Consider changing 'TECHNICAL SKILLS' to just 'SKILLS' for better standardization\"]}, \"ats_compatibility\": {\"score\": 85, \"issues\": [\"Pipe symbols in contact information may cause parsing issues\"], \"recommendations\": [\"Replace pipe symbols with spaces or bullet points in contact section\"]}, \"date_consistency\": {\"score\": 70, \"issues\": [\"Mixed date formats (Month YYYY and MM/YYYY)\", \"Some date ranges appear reversed\"], \"recommendations\": [\"Standardize all dates to MM/YYYY format\", \"Ensure all date ranges go from earliest to latest\"]}, \"contact_information\": {\"score\": 95, \"issues\": [\"Pipe symbols in contact line\"], \"recommendations\": [\"Separate contact elements with commas or bullet points instead of pipes\"]}, \"formatting_quality\": {\"score\": 80, \"issues\": [\"Some bullet points exceed 2 lines\", \"Inconsistent spacing between sections\"], \"recommendations\": [\"Shorten bullet points to maximum 2 lines\", \"Maintain consistent spacing between all sections\"]}}}, \"overall_analysis\": {\"overall_score\": 82.5, \"format_analysis\": {\"chronological_order\": {\"score\": 75, \"issues\": [\"Date inconsistencies in experience section\", \"Some dates appear to be reversed\"], \"recommendations\": [\"Fix date order in experience section\", \"Ensure all dates follow MM/YYYY format consistently\"]}, \"section_headings\": {\"score\": 90, \"issues\": [], \"recommendations\": [\"Consider changing 'TECHNICAL SKILLS' to just 'SKILLS' for better standardization\"]}, \"ats_compatibility\": {\"score\": 85, \"issues\": [\"Pipe symbols in contact information may cause parsing issues\"], \"recommendations\": [\"Replace pipe symbols with spaces or bullet points in contact section\"]}, \"date_consistency\": {\"score\": 70, \"issues\": [\"Mixed date formats (Month YYYY and MM/YYYY)\", \"Some date ranges appear reversed\"], \"recommendations\": [\"Standardize all dates to MM/YYYY format\", \"Ensure all date ranges go from earliest to latest\"]}, \"contact_information\": {\"score\": 95, \"issues\": [\"Pipe symbols in contact line\"], \"recommendations\": [\"Separate contact elements with commas or bullet points instead of pipes\"]}, \"formatting_quality\": {\"score\": 80, \"issues\": [\"Some bullet points exceed 2 lines\", \"Inconsistent spacing between sections\"], \"recommendations\": [\"Shorten bullet points to maximum 2 lines\", \"Maintain consistent spacing between all sections\"]}}, \"detailed_feedback\": [{\"issue\": \"Date ranges appear reversed in experience section (e.g., June 2023 \\u2013 August 2021)\", \"severity\": \"high\", \"recommendation\": \"Correct date ranges to show earlier date first\", \"section\": \"experience\"}, {\"issue\": \"Bullet points in experience section sometimes exceed 2 lines\", \"severity\": \"medium\", \"recommendation\": \"Condense bullet points to be more concise\", \"section\": \"experience\"}, {\"issue\": \"Contact information formatting uses non-standard separators\", \"severity\": \"low\", \"recommendation\": \"Use standard separators like commas or bullets instead of pipes\", \"section\": \"personal_info\"}, {\"issue\": \"Technical skills section uses semicolons and varying proficiency descriptions\", \"severity\": \"medium\", \"recommendation\": \"Use consistent format for skills listing, preferably as bullet points\", \"section\": \"skills\"}]}}"

Edit
openai_feedback
jsonb
"{\"content_issues\": [{\"issue\": \"Missing professional summary\", \"severity\": \"high\", \"recommendation\": \"Add a professional summary to provide a brief overview of your qualifications and career goals\", \"section\": \"professional_summary\", \"current_text\": null, \"suggested_improvement\": \"Motivated Computer Science graduate with experience in software development and online marketing. Proven ability to design and implement efficient and effective software solutions. Seeking to leverage my technical skills to contribute to a dynamic and innovative team.\"}, {\"issue\": \"Inconsistent date order\", \"severity\": \"medium\", \"recommendation\": \"Ensure that the start and end dates for each job are in the correct order\", \"section\": \"work_experience\", \"current_text\": \"\\\"start_date\\\": \\\"2023-06\\\", \\\"end_date\\\": \\\"2021-08\\\"\", \"suggested_improvement\": \"\\\"start_date\\\": \\\"2021-06\\\", \\\"end_date\\\": \\\"2023-08\\\"\"}, {\"issue\": \"Missing job descriptions\", \"severity\": \"medium\", \"recommendation\": \"Add a brief description for each job to provide context for your achievements\", \"section\": \"work_experience\", \"current_text\": \"\\\"description\\\": null\", \"suggested_improvement\": \"\\\"description\\\": \\\"As a Software Development Intern at Pros, I was responsible for designing and implementing REST API calls and refactoring existing API calls.\\\"\"}, {\"issue\": \"Missing soft skills\", \"severity\": \"low\", \"recommendation\": \"Include soft skills that are relevant to your field\", \"section\": \"skills\", \"current_text\": \"\\\"soft\\\": null\", \"suggested_improvement\": \"\\\"soft\\\": [\\\"Problem-solving\\\", \\\"Communication\\\", \\\"Teamwork\\\"]\"}, {\"issue\": \"Missing website link\", \"severity\": \"low\", \"recommendation\": \"Add a link to your personal website or portfolio\", \"section\": \"personal_info\", \"current_text\": \"\\\"website\\\": null\", \"suggested_improvement\": \"\\\"website\\\": \\\"www.teresatechnical.com\\\"\"}, {\"issue\": \"Missing professional summary\", \"severity\": \"medium\", \"recommendation\": \"Add a professional summary to provide a brief overview of your qualifications and career goals\", \"section\": \"professional_summary\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Inconsistent date order\", \"severity\": \"medium\", \"recommendation\": \"Ensure that the start and end dates for each job are in the correct order\", \"section\": \"work_experience\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing job descriptions\", \"severity\": \"medium\", \"recommendation\": \"Ensure that the start and end dates for each job are in the correct order\", \"section\": \"work_experience\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing honors\", \"severity\": \"medium\", \"recommendation\": \"Include any honors or awards received during your education\", \"section\": \"education\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing soft skills\", \"severity\": \"medium\", \"recommendation\": \"Include soft skills that are relevant to your field\", \"section\": \"skills\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing tools\", \"severity\": \"medium\", \"recommendation\": \"Include soft skills that are relevant to your field\", \"section\": \"skills\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing languages\", \"severity\": \"medium\", \"recommendation\": \"Include soft skills that are relevant to your field\", \"section\": \"skills\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing website link\", \"severity\": \"medium\", \"recommendation\": \"Add a link to your personal website or portfolio\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing project dates\", \"severity\": \"medium\", \"recommendation\": \"Add a link to your personal website or portfolio\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Missing project URLs\", \"severity\": \"medium\", \"recommendation\": \"Add a link to your personal website or portfolio\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}], \"content_analysis\": {\"professional_summary\": {\"score\": 0, \"issues\": [\"Missing professional summary\"], \"recommendations\": [\"Add a professional summary to provide a brief overview of your qualifications and career goals\"]}, \"work_experience\": {\"score\": 80, \"issues\": [\"Inconsistent date order\", \"Missing job descriptions\"], \"recommendations\": [\"Ensure that the start and end dates for each job are in the correct order\", \"Add a brief description for each job to provide context for your achievements\"]}, \"education\": {\"score\": 90, \"issues\": [\"Missing honors\"], \"recommendations\": [\"Include any honors or awards received during your education\"]}, \"skills\": {\"score\": 60, \"issues\": [\"Missing soft skills\", \"Missing tools\", \"Missing languages\"], \"recommendations\": [\"Include soft skills that are relevant to your field\", \"List any tools or software that you are proficient in\", \"Include any languages that you speak fluently\"]}, \"content_quality\": {\"score\": 70, \"issues\": [\"Missing website link\", \"Missing project dates\", \"Missing project URLs\"], \"recommendations\": [\"Add a link to your personal website or portfolio\", \"Include the start and end dates for each project\", \"Add URLs for each project so that employers can view your work\"]}}, \"overall_analysis\": {\"overall_content_score\": 70, \"content_analysis\": {\"professional_summary\": {\"score\": 0, \"issues\": [\"Missing professional summary\"], \"recommendations\": [\"Add a professional summary to provide a brief overview of your qualifications and career goals\"]}, \"work_experience\": {\"score\": 80, \"issues\": [\"Inconsistent date order\", \"Missing job descriptions\"], \"recommendations\": [\"Ensure that the start and end dates for each job are in the correct order\", \"Add a brief description for each job to provide context for your achievements\"]}, \"education\": {\"score\": 90, \"issues\": [\"Missing honors\"], \"recommendations\": [\"Include any honors or awards received during your education\"]}, \"skills\": {\"score\": 60, \"issues\": [\"Missing soft skills\", \"Missing tools\", \"Missing languages\"], \"recommendations\": [\"Include soft skills that are relevant to your field\", \"List any tools or software that you are proficient in\", \"Include any languages that you speak fluently\"]}, \"content_quality\": {\"score\": 70, \"issues\": [\"Missing website link\", \"Missing project dates\", \"Missing project URLs\"], \"recommendations\": [\"Add a link to your personal website or portfolio\", \"Include the start and end dates for each project\", \"Add URLs for each project so that employers can view your work\"]}}, \"detailed_feedback\": [{\"issue\": \"Missing professional summary\", \"severity\": \"high\", \"recommendation\": \"Add a professional summary to provide a brief overview of your qualifications and career goals\", \"section\": \"professional_summary\", \"current_text\": null, \"suggested_improvement\": \"Motivated Computer Science graduate with experience in software development and online marketing. Proven ability to design and implement efficient and effective software solutions. Seeking to leverage my technical skills to contribute to a dynamic and innovative team.\"}, {\"issue\": \"Inconsistent date order\", \"severity\": \"medium\", \"recommendation\": \"Ensure that the start and end dates for each job are in the correct order\", \"section\": \"work_experience\", \"current_text\": \"\\\"start_date\\\": \\\"2023-06\\\", \\\"end_date\\\": \\\"2021-08\\\"\", \"suggested_improvement\": \"\\\"start_date\\\": \\\"2021-06\\\", \\\"end_date\\\": \\\"2023-08\\\"\"}, {\"issue\": \"Missing job descriptions\", \"severity\": \"medium\", \"recommendation\": \"Add a brief description for each job to provide context for your achievements\", \"section\": \"work_experience\", \"current_text\": \"\\\"description\\\": null\", \"suggested_improvement\": \"\\\"description\\\": \\\"As a Software Development Intern at Pros, I was responsible for designing and implementing REST API calls and refactoring existing API calls.\\\"\"}, {\"issue\": \"Missing soft skills\", \"severity\": \"low\", \"recommendation\": \"Include soft skills that are relevant to your field\", \"section\": \"skills\", \"current_text\": \"\\\"soft\\\": null\", \"suggested_improvement\": \"\\\"soft\\\": [\\\"Problem-solving\\\", \\\"Communication\\\", \\\"Teamwork\\\"]\"}, {\"issue\": \"Missing website link\", \"severity\": \"low\", \"recommendation\": \"Add a link to your personal website or portfolio\", \"section\": \"personal_info\", \"current_text\": \"\\\"website\\\": null\", \"suggested_improvement\": \"\\\"website\\\": \\\"www.teresatechnical.com\\\"\"}]}}"

Edit
validation_status
varchar
validated

Gemini validation status: pending, validating, completed, failed

is_resume
bool

TRUE

TRUE
Gemini validation result: true if document is a resume, false if not

validation_likelihood
int4
90
Gemini confidence percentage (0-100)

validation_reason
text
Fallback validation - Gemini validation failed

Gemini explanation for validation result

validation_error
text
Validation failed: 403 Permission denied: Consumer 'api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I' has been suspended. [reason: "CONSUMER_SUSPENDED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
metadata {
  key: "containerInfo"
  value: "api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I"
}
metadata {
  key: "consumer"
  value: "projects/417913572512"
}
, locale: "en-US"
message: "Permission denied: Consumer \'api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I\' has been suspended."
]

Error message if validation failed

validated_at
timestamp

06/09/2025 09:51:13 AM

Timestamp when validation completed

content_analysis_status
varchar
completed

content_analyzed_at
timestamptz

06/09/2025 12:52:21 PM

Your local timezone will be automatically applied (+0300)

content_analysis_error
text

## Some of the railway logs
INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8 "HTTP/2 200 OK"

INFO:app.tasks.resume_analysis:🔍 Status determination - validation: validated, is_resume: True (type: <class 'bool'>), parsing: parsed, scoring: scored, content: pending

INFO:app.tasks.resume_analysis:🔄 Returning status: ready_for_content_analysis

INFO:     100.64.0.3:31704 - "GET /api/resumes/c8673db5-9e79-4a18-80b5-4dc7e82d79a8/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id%2C%20claude_score%2C%20claude_feedback%2C%20scoring_status&id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:     100.64.0.3:31704 - "GET /api/resumes/c8673db5-9e79-4a18-80b5-4dc7e82d79a8/claude-feedback HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8 "HTTP/2 200 OK"

INFO:app.tasks.resume_analysis:🔍 Status determination - validation: validated, is_resume: True (type: <class 'bool'>), parsing: parsed, scoring: scored, content: pending

INFO:app.tasks.resume_analysis:🔄 Returning status: ready_for_content_analysis

INFO:     100.64.0.3:31704 - "GET /api/resumes/c8673db5-9e79-4a18-80b5-4dc7e82d79a8/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id%2C%20claude_score%2C%20claude_feedback%2C%20scoring_status&id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:     100.64.0.3:31704 - "GET /api/resumes/c8673db5-9e79-4a18-80b5-4dc7e82d79a8/claude-feedback HTTP/1.1" 200 OK

[2025-06-09 09:52:21,335: INFO/ForkPoolWorker-1] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"

[2025-06-09 09:52:21,339: INFO/ForkPoolWorker-1] ✅ OpenAI analysis completed for resume c8673db5-9e79-4a18-80b5-4dc7e82d79a8, score: 70.0

[2025-06-09 09:52:21,933: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.c8673db5-9e79-4a18-80b5-4dc7e82d79a8 "HTTP/2 200 OK"

[2025-06-09 09:52:21,934: INFO/ForkPoolWorker-1] Successfully analyzed content for resume c8673db5-9e79-4a18-80b5-4dc7e82d79a8, score: 70.0

[2025-06-09 09:52:21,937: INFO/ForkPoolWorker-1] Task app.tasks.resume_analysis.analyze_content_task[44a49b19-ac93-416f-b3a0-0c5eb9619ddd] succeeded in 36.50088502885774s: {'status': 'success', 'resume_id': 'c8673db5-9e79-4a18-80b5-4dc7e82d79a8', 'content_score': 70.0, 'feedback_items': 15}

1:M 09 Jun 2025 10:05:51.707 * 100 changes in 300 seconds. Saving...

1:M 09 Jun 2025 10:05:51.708 * Background saving started by pid 134

134:C 09 Jun 2025 10:05:51.717 * DB saved on disk

134:C 09 Jun 2025 10:05:51.717 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 09 Jun 2025 10:05:51.808 * Background saving terminated with success

1:M 09 Jun 2025 11:05:52.045 * 1 changes in 3600 seconds. Saving...

1:M 09 Jun 2025 11:05:52.045 * Background saving started by pid 135

135:C 09 Jun 2025 11:05:52.055 * DB saved on disk

135:C 09 Jun 2025 11:05:52.056 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 09 Jun 2025 11:05:52.146 * Background saving terminated with success
 
## Gemini Prompt
Analyze the attached document to determine whether it is a resume or CV.
Evaluate the content using the following criteria:
Personal Information – Presence of full name, email, phone number, address, or LinkedIn.
Work Experience – Sections describing employment history, job titles, companies, and dates.
Education – Academic qualifications, degrees, schools/universities, graduation years.
Skills & Competencies – Lists or sections with hard/soft skills, tools, certifications.
Summary or Objective – An introductory statement outlining professional goals or background.
Resume Format – Structured layout typical of resumes (e.g., headings, bullet points, section titles).
Return your response in this exact JSON format only (no extra text, explanation, or formatting):
Rules:
"likelihood" must be an integer between 0–100.
"is_resume" should be true only if likelihood ≥ 70, otherwise false.
"confidence" should be "high" (likelihood ≥ 85), "medium" (50–84), or "low" (< 50).
"reason" must be short and clear (max 100 characters).
Output must be valid JSON only, with no surrounding commentary or markdown.
