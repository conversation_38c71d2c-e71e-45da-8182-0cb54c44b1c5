id
uuid
e4e5a7c5-917a-4835-bc8c-92b50da27326
user_id
uuid
f931c2e9-db27-4151-9c04-21ec6a8e4e50

Has a foreign key relation topublic.profiles.id
title
text
Eric <PERSON> resume.pdf

status
text
pending_analysis

created_at
timestamptz

06/10/2025 04:20:10 AM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

updated_at
timestamptz

06/10/2025 04:20:10 AM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

Optional Fields
These are columns that do not need any value

file_path
text
f931c2e9-db27-4151-9c04-21ec6a8e4e50/733faba7-1bef-414d-a1c4-6c37637df2e8.pdf

ats_score
int4
NULL
parsed_content
jsonb
NULL

Edit
Structured JSON content extracted from resume by <PERSON>

parsing_status
text
pending

Status of resume parsing: pending, parsing, parsed, failed

parsed_at
timestamptz

mm/dd/yyyy --:--:-- --

Timestamp when resume was successfully parsed

Your local timezone will be automatically applied (+0300)

parsing_error
text
NULL

Error message if parsing failed

overall_score
numeric
NULL
Overall resume score (0-100) calculated from Claude and OpenAI scores

claude_score
numeric
NULL
Format and structure score from Claude (25% of overall score)

openai_score
numeric
NULL
Content score from OpenAI (75% of overall score)

career_overview_score
numeric
NULL
Career overview section score (25% of OpenAI score)

experience_score
numeric
NULL
Experience section score (40% of OpenAI score)

education_score
numeric
NULL
Education section score (15% of OpenAI score)

additional_qualifications_score
numeric
NULL
Additional qualifications score (10% of OpenAI score)

content_quality_score
numeric
NULL
Content quality score (10% of OpenAI score)

scoring_feedback
jsonb
NULL

Edit
Detailed feedback and suggestions from AI analysis

scoring_status
text
pending

Status of resume scoring: pending, scoring, scored, failed

last_scored_at
timestamptz

mm/dd/yyyy --:--:-- --

Timestamp of the last scoring analysis

Your local timezone will be automatically applied (+0300)

scoring_error
text
NULL

Error message if scoring failed

claude_feedback
jsonb
NULL

Edit
openai_feedback
jsonb
NULL

Edit
validation_status
varchar
validated

Gemini validation status: pending, validating, completed, failed

is_resume
bool

FALSE

FALSE
Gemini validation result: true if document is a resume, false if not

validation_likelihood
int4
0
Gemini confidence percentage (0-100)

validation_reason
text
No resume sections found

Gemini explanation for validation result

validation_error
text
NULL

Error message if validation failed

validated_at
timestamp

06/10/2025 01:20:19 AM

Timestamp when validation completed

content_analysis_status
varchar
pending

content_analyzed_at
timestamptz

mm/dd/yyyy --:--:-- --

Your local timezone will be automatically applied (+0300)