id
uuid
2c2f4d51-d16b-4f1e-b090-77b5f4763ecf
user_id
uuid
f931c2e9-db27-4151-9c04-21ec6a8e4e50

Has a foreign key relation topublic.profiles.id
title
text
Eric <PERSON> resume.pdf

status
text
pending_analysis

created_at
timestamptz

06/06/2025 11:37:22 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

updated_at
timestamptz

06/06/2025 11:37:22 PM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

Optional Fields
These are columns that do not need any value

file_path
text
f931c2e9-db27-4151-9c04-21ec6a8e4e50/2ecd2932-7eb3-4cca-8fb4-7d4ea300cb6d.pdf

ats_score
int4
NULL
parsed_content
jsonb
"{\"personal_info\": {\"name\": \"<PERSON>", \"email\": \"<EMAIL>\", \"phone\": \"************\", \"location\": \"Santa Clara, CA\", \"linkedin\": null, \"github\": null, \"website\": null}, \"professional_summary\": \"Excellent time management, problem solving, organizational, and communication skills. Proficient in utilizing online social media and Microsoft Office. Familiar with HTML, PHP, CSS, C, and C++\", \"experience\": [{\"title\": \"IT Committee\", \"company\": \"Alpha Kappa Psi: Psi Omega Chapter\", \"location\": \"Santa Clara, CA\", \"start_date\": \"2012-06\", \"end_date\": \"Present\", \"description\": \"Exemplified leadership skills to delegate tasks and creativity to design the website layout using HTML and CSS skills. Currently designing online application process through the website for fraternity membership in order to streamline the rush process.\", \"achievements\": [], \"technologies\": [\"HTML\", \"CSS\"]}, {\"title\": \"Intern\", \"company\": \"InnoVentions: Connect2Dine\", \"location\": \"Santa Clara, CA\", \"start_date\": \"2011-12\", \"end_date\": \"2012-03\", \"description\": \"Designed product wireframe using HTML, PHP, and CSS. Advised the development of the usability interface of the website landing page and home page. Demonstrated team-work, communication, and organizational skills by working with programmers and business administrators during the design and implementation of product wireframe.\", \"achievements\": [], \"technologies\": [\"HTML\", \"PHP\", \"CSS\"]}, {\"title\": \"Marketing\", \"company\": \"Mercer Trade Inc.\", \"location\": \"Mercer Island, WA\", \"start_date\": \"2010-09\", \"end_date\": \"2011-06\", \"description\": \"Managed the event planning of a fundraising event for Seattle Adaptive Sports. Directed marketing campaigns during the school year to raise awareness about products that the company imported and the charity events that we held.\", \"achievements\": [], \"technologies\": []}, {\"title\": \"Manager\", \"company\": \"Mercer Island High School Student Store\", \"location\": \"Mercer Island, WA\", \"start_date\": \"2009-09\", \"end_date\": \"2011-06\", \"description\": \"Directed the designation of work to other students, and ran the daily operations of the business. Demonstrated detail-orientation, management skills, and the ability to handle customers in a professional manner.\", \"achievements\": [], \"technologies\": []}], \"education\": [{\"degree\": \"Bachelor of Science\", \"field\": \"Computer Science and Engineering\", \"institution\": \"Santa Clara University\", \"location\": \"Santa Clara, CA\", \"graduation_date\": \"2015-06\", \"gpa\": null, \"honors\": [\"Cisco-SCU Engineering Fellowship 2012\"]}], \"skills\": {\"technical\": [\"HTML\", \"PHP\", \"CSS\", \"C\", \"C++\"], \"soft\": [\"Time management\", \"Problem solving\", \"Organizational skills\", \"Communication skills\"], \"tools\": [\"Microsoft Office\"], \"languages\": []}, \"certifications\": [], \"projects\": [], \"additional_sections\": {\"volunteer_work\": [{\"role\": \"Fundraising Committee Chair\", \"organization\": \"Alpha Kappa Psi: Psi Omega Chapter\", \"duration\": \"2012-04 to 2012-06\", \"description\": \"Facilitated and oversaw fundraising activities for the Omicron pledge class demonstrating management, communication, and time management skills. Raised $2200 over six weeks for the chapter.\"}], \"publications\": [], \"awards\": [], \"languages\": [], \"interests\": []}}"

Edit
Structured JSON content extracted from resume by Claude

parsing_status
text
parsed

Status of resume parsing: pending, parsing, parsed, failed

parsed_at
timestamptz

06/06/2025 11:38:32 PM

Timestamp when resume was successfully parsed

Your local timezone will be automatically applied (+0300)

parsing_error
text
NULL

Error message if parsing failed

overall_score
numeric
NULL
Overall resume score (0-100) calculated from Claude and OpenAI scores

claude_score
numeric
82.50
Format and structure score from Claude (25% of overall score)

openai_score
numeric
NULL
Content score from OpenAI (75% of overall score)

career_overview_score
numeric
NULL
Career overview section score (25% of OpenAI score)

experience_score
numeric
NULL
Experience section score (40% of OpenAI score)

education_score
numeric
NULL
Education section score (15% of OpenAI score)

additional_qualifications_score
numeric
NULL
Additional qualifications score (10% of OpenAI score)

content_quality_score
numeric
NULL
Content quality score (10% of OpenAI score)

scoring_feedback
jsonb
NULL

Edit
Detailed feedback and suggestions from AI analysis

scoring_status
text
scored

Status of resume scoring: pending, scoring, scored, failed

last_scored_at
timestamptz

06/06/2025 11:38:43 PM

Timestamp of the last scoring analysis

Your local timezone will be automatically applied (+0300)

scoring_error
text
NULL

Error message if scoring failed

claude_feedback
jsonb
"{\"format_issues\": [{\"issue\": \"Non-standard section heading\", \"severity\": \"medium\", \"recommendation\": \"Replace 'Qualifications and Capabilities' with 'Professional Summary'\", \"section\": \"summary\"}, {\"issue\": \"Inconsistent date formatting\", \"severity\": \"medium\", \"recommendation\": \"Use consistent 'Month YYYY' format throughout resume\", \"section\": \"experience\"}, {\"issue\": \"Long bullet points\", \"severity\": \"low\", \"recommendation\": \"Condense bullet points to be more concise and under 2 lines\", \"section\": \"experience\"}, {\"issue\": \"Missing LinkedIn/Professional URLs\", \"severity\": \"low\", \"recommendation\": \"Add LinkedIn profile URL to contact information\", \"section\": \"contact\"}, {\"issue\": \"'Qualifications and Capabilities' is non-standard heading\", \"severity\": \"medium\", \"recommendation\": \"Change to 'Professional Summary' or 'Summary of Qualifications'\", \"section\": \"section_headings\"}, {\"issue\": \"Contact information formatting could be simplified\", \"severity\": \"medium\", \"recommendation\": \"Remove vertical bars in contact line, use single line format\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Mixed date formats (Month YYYY and MM/YYYY)\", \"severity\": \"medium\", \"recommendation\": \"Standardize all dates to 'Month YYYY' format\", \"section\": \"date_consistency\"}, {\"issue\": \"Contact info uses separator symbols\", \"severity\": \"medium\", \"recommendation\": \"Use simple line breaks between contact elements\", \"section\": \"contact_information\"}, {\"issue\": \"Some bullet points exceed 2 lines\", \"severity\": \"medium\", \"recommendation\": \"Shorten bullet points to maximum 2 lines\", \"section\": \"formatting_quality\"}, {\"issue\": \"Inconsistent bullet point symbols\", \"severity\": \"medium\", \"recommendation\": \"Shorten bullet points to maximum 2 lines\", \"section\": \"formatting_quality\"}], \"ats_compatibility\": {\"score\": 82.5, \"analysis\": {\"chronological_order\": {\"score\": 95, \"issues\": [], \"recommendations\": []}, \"section_headings\": {\"score\": 85, \"issues\": [\"'Qualifications and Capabilities' is non-standard heading\"], \"recommendations\": [\"Change to 'Professional Summary' or 'Summary of Qualifications'\"]}, \"ats_compatibility\": {\"score\": 85, \"issues\": [\"Contact information formatting could be simplified\"], \"recommendations\": [\"Remove vertical bars in contact line, use single line format\"]}, \"date_consistency\": {\"score\": 75, \"issues\": [\"Mixed date formats (Month YYYY and MM/YYYY)\"], \"recommendations\": [\"Standardize all dates to 'Month YYYY' format\"]}, \"contact_information\": {\"score\": 90, \"issues\": [\"Contact info uses separator symbols\"], \"recommendations\": [\"Use simple line breaks between contact elements\"]}, \"formatting_quality\": {\"score\": 65, \"issues\": [\"Some bullet points exceed 2 lines\", \"Inconsistent bullet point symbols\"], \"recommendations\": [\"Shorten bullet points to maximum 2 lines\", \"Use consistent bullet point formatting throughout\"]}}}, \"overall_analysis\": {\"overall_score\": 82.5, \"format_analysis\": {\"chronological_order\": {\"score\": 95, \"issues\": [], \"recommendations\": []}, \"section_headings\": {\"score\": 85, \"issues\": [\"'Qualifications and Capabilities' is non-standard heading\"], \"recommendations\": [\"Change to 'Professional Summary' or 'Summary of Qualifications'\"]}, \"ats_compatibility\": {\"score\": 85, \"issues\": [\"Contact information formatting could be simplified\"], \"recommendations\": [\"Remove vertical bars in contact line, use single line format\"]}, \"date_consistency\": {\"score\": 75, \"issues\": [\"Mixed date formats (Month YYYY and MM/YYYY)\"], \"recommendations\": [\"Standardize all dates to 'Month YYYY' format\"]}, \"contact_information\": {\"score\": 90, \"issues\": [\"Contact info uses separator symbols\"], \"recommendations\": [\"Use simple line breaks between contact elements\"]}, \"formatting_quality\": {\"score\": 65, \"issues\": [\"Some bullet points exceed 2 lines\", \"Inconsistent bullet point symbols\"], \"recommendations\": [\"Shorten bullet points to maximum 2 lines\", \"Use consistent bullet point formatting throughout\"]}}, \"detailed_feedback\": [{\"issue\": \"Non-standard section heading\", \"severity\": \"medium\", \"recommendation\": \"Replace 'Qualifications and Capabilities' with 'Professional Summary'\", \"section\": \"summary\"}, {\"issue\": \"Inconsistent date formatting\", \"severity\": \"medium\", \"recommendation\": \"Use consistent 'Month YYYY' format throughout resume\", \"section\": \"experience\"}, {\"issue\": \"Long bullet points\", \"severity\": \"low\", \"recommendation\": \"Condense bullet points to be more concise and under 2 lines\", \"section\": \"experience\"}, {\"issue\": \"Missing LinkedIn/Professional URLs\", \"severity\": \"low\", \"recommendation\": \"Add LinkedIn profile URL to contact information\", \"section\": \"contact\"}]}}"

Edit
openai_feedback
jsonb
NULL

Edit
validation_status
varchar
validated

Gemini validation status: pending, validating, completed, failed

is_resume
bool

TRUE

TRUE
Gemini validation result: true if document is a resume, false if not

validation_likelihood
int4
90
Gemini confidence percentage (0-100)

validation_reason
text
Fallback validation - Gemini validation failed

Gemini explanation for validation result

validation_error
text
{'code': '23514', 'details': 'Failing row contains (2c2f4d51-d16b-4f1e-b090-77b5f4763ecf, f931c2e9-db27-4151-9c04-21ec6a8e4e50, Eric Chang resume.pdf, pending_analysis, f931c2e9-db27-4151-9c04-21ec6a8e4e50/2ecd2932-7eb3-4cca-8fb4-7d4..., null, 2025-06-06 20:37:22.958227+00, 2025-06-06 20:37:22.958227+00, null, skipped, null, null, null, null, null, null, null, null, null, null, null, pending, null, null, null, null, validated, f, 0, No relevant information found, null, 2025-06-06 20:38:15.095344).', 'hint': None, 'message': 'new row for relation "resumes" violates check constraint "resumes_parsing_status_check"'}

Error message if validation failed

validated_at
timestamp

06/06/2025 08:38:16 PM

Timestamp when validation completed

## Railway Logs
INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

[2025-06-06 20:38:15,095: INFO/ForkPoolWorker-1] Gemini validation completed: {'is_resume': False, 'likelihood': 0, 'reason': 'No relevant information found', 'confidence': 'low', 'validation_method': 'gemini_1_5_flash'}

[2025-06-06 20:38:15,638: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:15,639: WARNING/ForkPoolWorker-1] Document is not a resume: No relevant information found

[2025-06-06 20:38:16,196: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 400 Bad Request"

[2025-06-06 20:38:17,247: WARNING/ForkPoolWorker-1] Using fallback validation due to error: {'is_resume': True, 'likelihood': 90, 'reason': 'Fallback validation - Gemini validation failed'}

[2025-06-06 20:38:16,196: ERROR/ForkPoolWorker-1] Gemini validation failed: {'code': '23514', 'details': 'Failing row contains (2c2f4d51-d16b-4f1e-b090-77b5f4763ecf, f931c2e9-db27-4151-9c04-21ec6a8e4e50, Eric Chang resume.pdf, pending_analysis, f931c2e9-db27-4151-9c04-21ec6a8e4e50/2ecd2932-7eb3-4cca-8fb4-7d4..., null, 2025-06-06 20:37:22.958227+00, 2025-06-06 20:37:22.958227+00, null, skipped, null, null, null, null, null, null, null, null, null, null, null, pending, null, null, null, null, validated, f, 0, No relevant information found, null, 2025-06-06 20:38:15.095344).', 'hint': None, 'message': 'new row for relation "resumes" violates check constraint "resumes_parsing_status_check"'}

[2025-06-06 20:38:17,247: INFO/ForkPoolWorker-1] Validation completed, proceeding with parsing for resume 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf

[2025-06-06 20:38:16,720: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:17,777: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:17,246: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:17,779: INFO/ForkPoolWorker-1] Extracting full text from validated resume: f931c2e9-db27-4151-9c04-21ec6a8e4e50/2ecd2932-7eb3-4cca-8fb4-7d4ea300cb6d.pdf

[2025-06-06 20:38:17,896: INFO/ForkPoolWorker-1] Parsing resume content with Claude for resume_id: 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/auth/v1/user "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=id%2C%20user_id&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf&user_id=eq.f931c2e9-db27-4151-9c04-21ec6a8e4e50 "HTTP/2 200 OK"

INFO:httpx:HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=validation_status%2C%20is_resume%2C%20validation_reason%2C%20parsing_status%2C%20scoring_status%2C%20validated_at%2C%20parsed_at%2C%20last_scored_at%2C%20parsing_error%2C%20scoring_error%2C%20validation_error&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

INFO:     **********:24516 - "GET /api/resumes/2c2f4d51-d16b-4f1e-b090-77b5f4763ecf/analysis-status HTTP/1.1" 200 OK

[2025-06-06 20:38:32,533: INFO/ForkPoolWorker-1] HTTP Request: POST https://api.anthropic.com/v1/messages "HTTP/1.1 200 OK"

[2025-06-06 20:38:33,079: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:33,081: INFO/ForkPoolWorker-1] Successfully parsed resume 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf

[2025-06-06 20:38:33,094: INFO/ForkPoolWorker-1] Task app.tasks.resume_analysis.parse_resume_task[a6c26f20-9972-4951-986c-3013a0b95ee3] succeeded in 66.36624942580238s: {'status': 'success', 'resume_id': '2c2f4d51-d16b-4f1e-b090-77b5f4763ecf', 'parsed_sections': ['personal_info', 'professional_summary', 'experience', 'education', 'skills', 'certifications', 'projects', 'additional_sections']}

[2025-06-06 20:38:33,101: INFO/MainProcess] Task app.tasks.resume_analysis.score_format_task[d1ce38bb-9971-4daa-8063-58459b619da0] received

[2025-06-06 20:38:33,105: INFO/ForkPoolWorker-1] Starting format scoring for resume_id: 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf

[2025-06-06 20:38:33,621: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:34,131: INFO/ForkPoolWorker-1] HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?select=parsed_content%2C%20file_path&id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:34,490: INFO/ForkPoolWorker-1] HTTP Request: GET https://xmmbjopzpyotkyyfqzyq.supabase.co/storage/v1/object/resumes/f931c2e9-db27-4151-9c04-21ec6a8e4e50/2ecd2932-7eb3-4cca-8fb4-7d4ea300cb6d.pdf "HTTP/2 200 OK"

[2025-06-06 20:38:34,565: INFO/ForkPoolWorker-1] Scoring format with Claude for resume_id: 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf

[2025-06-06 20:38:43,520: INFO/ForkPoolWorker-1] HTTP Request: POST https://api.anthropic.com/v1/messages "HTTP/1.1 200 OK"

[2025-06-06 20:38:44,043: INFO/ForkPoolWorker-1] HTTP Request: PATCH https://xmmbjopzpyotkyyfqzyq.supabase.co/rest/v1/resumes?id=eq.2c2f4d51-d16b-4f1e-b090-77b5f4763ecf "HTTP/2 200 OK"

[2025-06-06 20:38:44,044: INFO/ForkPoolWorker-1] Successfully scored format for resume 2c2f4d51-d16b-4f1e-b090-77b5f4763ecf, score: 82.5

[2025-06-06 20:38:44,053: INFO/ForkPoolWorker-1] Task app.tasks.resume_analysis.score_format_task[d1ce38bb-9971-4daa-8063-58459b619da0] succeeded in 10.951605582144111s: {'status': 'success', 'resume_id': '2c2f4d51-d16b-4f1e-b090-77b5f4763ecf', 'claude_score': 82.5, 'feedback_items': 10}