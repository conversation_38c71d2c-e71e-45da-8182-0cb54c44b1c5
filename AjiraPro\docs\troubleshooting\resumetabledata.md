id
uuid
bcc4a587-00a6-4686-8a2b-ba4ab58f5118
user_id
uuid
f931c2e9-db27-4151-9c04-21ec6a8e4e50

Has a foreign key relation topublic.profiles.id
title
text
Resume - <PERSON>.pdf

status
text
pending_analysis

created_at
timestamptz

06/10/2025 02:27:27 AM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

updated_at
timestamptz

06/10/2025 02:27:27 AM

Default: timezone('utc'::text, now())

Your local timezone will be automatically applied (+0300)

Optional Fields
These are columns that do not need any value

file_path
text
f931c2e9-db27-4151-9c04-21ec6a8e4e50/5c2f6ce5-3276-434a-af96-3a5ad69d36a0.pdf

ats_score
int4
NULL
parsed_content
jsonb
"{\"personal_info\": {\"name\": null, \"email\": \"<EMAIL>\", \"phone\": \"+254 786 178372\", \"location\": \"Nairobi, Kenya\", \"linkedin\": null, \"github\": null, \"website\": \"www.XGAMERtechnologies.com\"}, \"professional_summary\": \"Dealers in PC, PsP, Ps2, Ps3, Ps4, Xbox games, Graphics cards, Gamepads, Joystics, Assembly of High end Rendering & Gaming Desktops, Sale & Repair of Computers, Playstations, XBOX etc\", \"experience\": [], \"education\": [], \"skills\": {\"technical\": [\"Assembly of High end Rendering & Gaming Desktops\", \"Computer Repair\", \"PlayStation Repair\", \"Xbox Repair\"], \"soft\": [], \"tools\": [], \"languages\": []}, \"certifications\": [], \"projects\": [], \"additional_sections\": {\"business_details\": {\"company_name\": \"XGAMERtechnologies\", \"vat_pin\": \"P051741660D\", \"address\": \"Shop number 501 (5th floor), Veteran House (Graffins college), Along Moi Avenue\", \"bank_details\": {\"bank_name\": \"I&M Bank LTD\", \"account_name\": \"XGAMERtechnologies (K) LIMITED\", \"account_number\": \"**************\"}}, \"volunteer_work\": [], \"publications\": [], \"awards\": [], \"languages\": [], \"interests\": []}}"

Edit
Structured JSON content extracted from resume by Claude

parsing_status
text
parsed

Status of resume parsing: pending, parsing, parsed, failed

parsed_at
timestamptz

06/10/2025 02:27:59 AM

Timestamp when resume was successfully parsed

Your local timezone will be automatically applied (+0300)

parsing_error
text
NULL

Error message if parsing failed

overall_score
numeric
32.30
Overall resume score (0-100) calculated from Claude and OpenAI scores

claude_score
numeric
12.40
Format and structure score from Claude (25% of overall score)

openai_score
numeric
NULL
Content score from OpenAI (75% of overall score)

career_overview_score
numeric
NULL
Career overview section score (25% of OpenAI score)

experience_score
numeric
NULL
Experience section score (40% of OpenAI score)

education_score
numeric
NULL
Education section score (15% of OpenAI score)

additional_qualifications_score
numeric
NULL
Additional qualifications score (10% of OpenAI score)

content_quality_score
numeric
45.60
Content quality score (10% of OpenAI score)

scoring_feedback
jsonb
NULL

Edit
Detailed feedback and suggestions from AI analysis

scoring_status
text
scored

Status of resume scoring: pending, scoring, scored, failed

last_scored_at
timestamptz

06/10/2025 02:27:59 AM

Timestamp of the last scoring analysis

Your local timezone will be automatically applied (+0300)

scoring_error
text
NULL

Error message if scoring failed

claude_feedback
jsonb
"{\"format_issues\": [{\"issue\": \"Document is a business quotation, not a resume\", \"severity\": \"high\", \"recommendation\": \"Create new document using proper resume format and content\", \"section\": \"entire_document\"}, {\"issue\": \"Contains business pricing tables and forms\", \"severity\": \"high\", \"recommendation\": \"Remove all tables and business forms, replace with proper resume sections\", \"section\": \"formatting\"}, {\"issue\": \"Missing all standard resume sections and content\", \"severity\": \"high\", \"recommendation\": \"Add proper sections: Summary, Experience, Education, Skills with relevant content\", \"section\": \"content\"}, {\"issue\": \"This is not a resume - appears to be a sales quotation document\", \"severity\": \"medium\", \"recommendation\": \"Create proper resume with work history in reverse chronological order\", \"section\": \"chronological_order\"}, {\"issue\": \"No chronological work history present\", \"severity\": \"medium\", \"recommendation\": \"Create proper resume with work history in reverse chronological order\", \"section\": \"chronological_order\"}, {\"issue\": \"No standard resume section headings present\", \"severity\": \"medium\", \"recommendation\": \"Add proper resume sections: Summary, Experience, Education, Skills\", \"section\": \"section_headings\"}, {\"issue\": \"Contains only business quotation headers\", \"severity\": \"medium\", \"recommendation\": \"Add proper resume sections: Summary, Experience, Education, Skills\", \"section\": \"section_headings\"}, {\"issue\": \"Contains tables and pricing layouts\", \"severity\": \"medium\", \"recommendation\": \"Remove all tables and forms\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Uses non-standard formatting\", \"severity\": \"medium\", \"recommendation\": \"Remove all tables and forms\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Includes business forms and calculations\", \"severity\": \"medium\", \"recommendation\": \"Remove all tables and forms\", \"section\": \"ats_compatibility\"}, {\"issue\": \"Uses inconsistent date formats\", \"severity\": \"medium\", \"recommendation\": \"Use consistent MM/YYYY format for work history\", \"section\": \"date_consistency\"}, {\"issue\": \"Contains timestamps\", \"severity\": \"medium\", \"recommendation\": \"Use consistent MM/YYYY format for work history\", \"section\": \"date_consistency\"}, {\"issue\": \"Dates used are for quotes not work history\", \"severity\": \"medium\", \"recommendation\": \"Use consistent MM/YYYY format for work history\", \"section\": \"date_consistency\"}, {\"issue\": \"Business contact info instead of personal\", \"severity\": \"medium\", \"recommendation\": \"Include personal contact information only\", \"section\": \"contact_information\"}, {\"issue\": \"Contains VAT numbers and business details\", \"severity\": \"medium\", \"recommendation\": \"Include personal contact information only\", \"section\": \"contact_information\"}, {\"issue\": \"Multiple phone/contact formats\", \"severity\": \"medium\", \"recommendation\": \"Include personal contact information only\", \"section\": \"contact_information\"}, {\"issue\": \"Heavy use of tables and forms\", \"severity\": \"medium\", \"recommendation\": \"Convert to clean resume format\", \"section\": \"formatting_quality\"}, {\"issue\": \"Inconsistent spacing\", \"severity\": \"medium\", \"recommendation\": \"Convert to clean resume format\", \"section\": \"formatting_quality\"}, {\"issue\": \"Mixed case formatting\", \"severity\": \"medium\", \"recommendation\": \"Convert to clean resume format\", \"section\": \"formatting_quality\"}, {\"issue\": \"Non-standard document structure\", \"severity\": \"medium\", \"recommendation\": \"Convert to clean resume format\", \"section\": \"formatting_quality\"}], \"ats_compatibility\": {\"score\": 12.4, \"analysis\": {\"chronological_order\": {\"score\": 0, \"issues\": [\"This is not a resume - appears to be a sales quotation document\", \"No chronological work history present\"], \"recommendations\": [\"Create proper resume with work history in reverse chronological order\", \"Include employment dates and progression\"]}, \"section_headings\": {\"score\": 5.5, \"issues\": [\"No standard resume section headings present\", \"Contains only business quotation headers\"], \"recommendations\": [\"Add proper resume sections: Summary, Experience, Education, Skills\", \"Use clear, standard section headings\"]}, \"ats_compatibility\": {\"score\": 15.3, \"issues\": [\"Contains tables and pricing layouts\", \"Uses non-standard formatting\", \"Includes business forms and calculations\"], \"recommendations\": [\"Remove all tables and forms\", \"Convert to simple text format\", \"Use standard resume structure\"]}, \"date_consistency\": {\"score\": 23.1, \"issues\": [\"Uses inconsistent date formats\", \"Contains timestamps\", \"Dates used are for quotes not work history\"], \"recommendations\": [\"Use consistent MM/YYYY format for work history\", \"Remove timestamps and quotation dates\"]}, \"contact_information\": {\"score\": 18.6, \"issues\": [\"Business contact info instead of personal\", \"Contains VAT numbers and business details\", \"Multiple phone/contact formats\"], \"recommendations\": [\"Include personal contact information only\", \"Remove business registration details\", \"Format contact info consistently\"]}, \"formatting_quality\": {\"score\": 11.9, \"issues\": [\"Heavy use of tables and forms\", \"Inconsistent spacing\", \"Mixed case formatting\", \"Non-standard document structure\"], \"recommendations\": [\"Convert to clean resume format\", \"Use consistent spacing and alignment\", \"Remove all tables and forms\"]}}}, \"overall_analysis\": {\"overall_score\": 12.4, \"format_analysis\": {\"chronological_order\": {\"score\": 0, \"issues\": [\"This is not a resume - appears to be a sales quotation document\", \"No chronological work history present\"], \"recommendations\": [\"Create proper resume with work history in reverse chronological order\", \"Include employment dates and progression\"]}, \"section_headings\": {\"score\": 5.5, \"issues\": [\"No standard resume section headings present\", \"Contains only business quotation headers\"], \"recommendations\": [\"Add proper resume sections: Summary, Experience, Education, Skills\", \"Use clear, standard section headings\"]}, \"ats_compatibility\": {\"score\": 15.3, \"issues\": [\"Contains tables and pricing layouts\", \"Uses non-standard formatting\", \"Includes business forms and calculations\"], \"recommendations\": [\"Remove all tables and forms\", \"Convert to simple text format\", \"Use standard resume structure\"]}, \"date_consistency\": {\"score\": 23.1, \"issues\": [\"Uses inconsistent date formats\", \"Contains timestamps\", \"Dates used are for quotes not work history\"], \"recommendations\": [\"Use consistent MM/YYYY format for work history\", \"Remove timestamps and quotation dates\"]}, \"contact_information\": {\"score\": 18.6, \"issues\": [\"Business contact info instead of personal\", \"Contains VAT numbers and business details\", \"Multiple phone/contact formats\"], \"recommendations\": [\"Include personal contact information only\", \"Remove business registration details\", \"Format contact info consistently\"]}, \"formatting_quality\": {\"score\": 11.9, \"issues\": [\"Heavy use of tables and forms\", \"Inconsistent spacing\", \"Mixed case formatting\", \"Non-standard document structure\"], \"recommendations\": [\"Convert to clean resume format\", \"Use consistent spacing and alignment\", \"Remove all tables and forms\"]}}, \"detailed_feedback\": [{\"issue\": \"Document is a business quotation, not a resume\", \"severity\": \"high\", \"recommendation\": \"Create new document using proper resume format and content\", \"section\": \"entire_document\"}, {\"issue\": \"Contains business pricing tables and forms\", \"severity\": \"high\", \"recommendation\": \"Remove all tables and business forms, replace with proper resume sections\", \"section\": \"formatting\"}, {\"issue\": \"Missing all standard resume sections and content\", \"severity\": \"high\", \"recommendation\": \"Add proper sections: Summary, Experience, Education, Skills with relevant content\", \"section\": \"content\"}]}}"

Edit
openai_feedback
jsonb
"{\"content_issues\": [{\"issue\": \"Incomplete personal information\", \"severity\": \"high\", \"recommendation\": \"Complete personal information, including name and LinkedIn profile\", \"section\": \"personal_info\", \"current_text\": \"name: null, linkedin: null\", \"suggested_improvement\": \"name: [Your Name], linkedin: [Your LinkedIn Profile]\"}, {\"issue\": \"Lack of specificity and value proposition in professional summary\", \"severity\": \"medium\", \"recommendation\": \"Provide a clear, concise summary of your professional background and skills. Highlight your unique value proposition\", \"section\": \"professional_summary\", \"current_text\": \"Dealers in PC, PsP, Ps2, Ps3, Ps4, Xbox games, Graphics cards, Gamepads, Joystics, Assembly of High end Rendering & Gaming Desktops, Sale & Repair of Computers, Playstations, XBOX etc\", \"suggested_improvement\": \"Experienced in dealing with a wide range of gaming technologies, including PC, PlayStation, and Xbox. Specialized in assembling high-end rendering and gaming desktops, and providing repair services for computers and gaming consoles.\"}, {\"issue\": \"No work experience listed\", \"severity\": \"high\", \"recommendation\": \"List your work experience, including company name, your role, duration, and key achievements\", \"section\": \"experience\", \"current_text\": \"experience: []\", \"suggested_improvement\": \"experience: [{company: [Company Name], role: [Your Role], duration: [Start Date - End Date], achievements: [Key Achievements]}]\"}, {\"issue\": \"No education details provided\", \"severity\": \"high\", \"recommendation\": \"Include your educational background, including institution name, degree obtained, and year of graduation\", \"section\": \"education\", \"current_text\": \"education: []\", \"suggested_improvement\": \"education: [{institution: [Institution Name], degree: [Degree Obtained], graduation_year: [Year of Graduation]}]\"}, {\"issue\": \"Lack of soft skills and languages\", \"severity\": \"medium\", \"recommendation\": \"Include relevant soft skills and list any languages you are proficient in\", \"section\": \"skills\", \"current_text\": \"soft: [], languages: []\", \"suggested_improvement\": \"soft: [List of Soft Skills], languages: [List of Languages]\"}, {\"issue\": \"Lack of professional tone and keyword optimization\", \"severity\": \"medium\", \"recommendation\": \"Maintain a professional tone throughout the resume and optimize the content with relevant keywords\", \"section\": \"overall_content\", \"current_text\": \"N/A\", \"suggested_improvement\": \"N/A\"}, {\"issue\": \"Lack of specificity and value proposition\", \"severity\": \"medium\", \"recommendation\": \"Provide a clear, concise summary of your professional background and skills\", \"section\": \"professional_summary\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Summary reads more like a list of services than a professional summary\", \"severity\": \"medium\", \"recommendation\": \"Provide a clear, concise summary of your professional background and skills\", \"section\": \"professional_summary\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"No work experience listed\", \"severity\": \"medium\", \"recommendation\": \"List your work experience, including company name, your role, duration, and key achievements\", \"section\": \"work_experience\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"No education details provided\", \"severity\": \"medium\", \"recommendation\": \"Include your educational background, including institution name, degree obtained, and year of graduation\", \"section\": \"education\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Lack of soft skills\", \"severity\": \"medium\", \"recommendation\": \"Include relevant soft skills\", \"section\": \"skills\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"No languages listed\", \"severity\": \"medium\", \"recommendation\": \"Include relevant soft skills\", \"section\": \"skills\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Incomplete personal information\", \"severity\": \"medium\", \"recommendation\": \"Complete personal information, including name and LinkedIn profile\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Lack of professional tone\", \"severity\": \"medium\", \"recommendation\": \"Complete personal information, including name and LinkedIn profile\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}, {\"issue\": \"Lack of keyword optimization\", \"severity\": \"medium\", \"recommendation\": \"Complete personal information, including name and LinkedIn profile\", \"section\": \"content_quality\", \"current_text\": \"\", \"suggested_improvement\": \"\"}], \"content_analysis\": {\"professional_summary\": {\"score\": 60.0, \"issues\": [\"Lack of specificity and value proposition\", \"Summary reads more like a list of services than a professional summary\"], \"recommendations\": [\"Provide a clear, concise summary of your professional background and skills\", \"Highlight your unique value proposition\"]}, \"work_experience\": {\"score\": 0.0, \"issues\": [\"No work experience listed\"], \"recommendations\": [\"List your work experience, including company name, your role, duration, and key achievements\"]}, \"education\": {\"score\": 0.0, \"issues\": [\"No education details provided\"], \"recommendations\": [\"Include your educational background, including institution name, degree obtained, and year of graduation\"]}, \"skills\": {\"score\": 70.0, \"issues\": [\"Lack of soft skills\", \"No languages listed\"], \"recommendations\": [\"Include relevant soft skills\", \"List any languages you are proficient in\"]}, \"content_quality\": {\"score\": 50.0, \"issues\": [\"Incomplete personal information\", \"Lack of professional tone\", \"Lack of keyword optimization\"], \"recommendations\": [\"Complete personal information, including name and LinkedIn profile\", \"Maintain a professional tone throughout the resume\", \"Optimize the content with relevant keywords\"]}}, \"overall_analysis\": {\"overall_content_score\": 45.6, \"content_analysis\": {\"professional_summary\": {\"score\": 60.0, \"issues\": [\"Lack of specificity and value proposition\", \"Summary reads more like a list of services than a professional summary\"], \"recommendations\": [\"Provide a clear, concise summary of your professional background and skills\", \"Highlight your unique value proposition\"]}, \"work_experience\": {\"score\": 0.0, \"issues\": [\"No work experience listed\"], \"recommendations\": [\"List your work experience, including company name, your role, duration, and key achievements\"]}, \"education\": {\"score\": 0.0, \"issues\": [\"No education details provided\"], \"recommendations\": [\"Include your educational background, including institution name, degree obtained, and year of graduation\"]}, \"skills\": {\"score\": 70.0, \"issues\": [\"Lack of soft skills\", \"No languages listed\"], \"recommendations\": [\"Include relevant soft skills\", \"List any languages you are proficient in\"]}, \"content_quality\": {\"score\": 50.0, \"issues\": [\"Incomplete personal information\", \"Lack of professional tone\", \"Lack of keyword optimization\"], \"recommendations\": [\"Complete personal information, including name and LinkedIn profile\", \"Maintain a professional tone throughout the resume\", \"Optimize the content with relevant keywords\"]}}, \"detailed_feedback\": [{\"issue\": \"Incomplete personal information\", \"severity\": \"high\", \"recommendation\": \"Complete personal information, including name and LinkedIn profile\", \"section\": \"personal_info\", \"current_text\": \"name: null, linkedin: null\", \"suggested_improvement\": \"name: [Your Name], linkedin: [Your LinkedIn Profile]\"}, {\"issue\": \"Lack of specificity and value proposition in professional summary\", \"severity\": \"medium\", \"recommendation\": \"Provide a clear, concise summary of your professional background and skills. Highlight your unique value proposition\", \"section\": \"professional_summary\", \"current_text\": \"Dealers in PC, PsP, Ps2, Ps3, Ps4, Xbox games, Graphics cards, Gamepads, Joystics, Assembly of High end Rendering & Gaming Desktops, Sale & Repair of Computers, Playstations, XBOX etc\", \"suggested_improvement\": \"Experienced in dealing with a wide range of gaming technologies, including PC, PlayStation, and Xbox. Specialized in assembling high-end rendering and gaming desktops, and providing repair services for computers and gaming consoles.\"}, {\"issue\": \"No work experience listed\", \"severity\": \"high\", \"recommendation\": \"List your work experience, including company name, your role, duration, and key achievements\", \"section\": \"experience\", \"current_text\": \"experience: []\", \"suggested_improvement\": \"experience: [{company: [Company Name], role: [Your Role], duration: [Start Date - End Date], achievements: [Key Achievements]}]\"}, {\"issue\": \"No education details provided\", \"severity\": \"high\", \"recommendation\": \"Include your educational background, including institution name, degree obtained, and year of graduation\", \"section\": \"education\", \"current_text\": \"education: []\", \"suggested_improvement\": \"education: [{institution: [Institution Name], degree: [Degree Obtained], graduation_year: [Year of Graduation]}]\"}, {\"issue\": \"Lack of soft skills and languages\", \"severity\": \"medium\", \"recommendation\": \"Include relevant soft skills and list any languages you are proficient in\", \"section\": \"skills\", \"current_text\": \"soft: [], languages: []\", \"suggested_improvement\": \"soft: [List of Soft Skills], languages: [List of Languages]\"}, {\"issue\": \"Lack of professional tone and keyword optimization\", \"severity\": \"medium\", \"recommendation\": \"Maintain a professional tone throughout the resume and optimize the content with relevant keywords\", \"section\": \"overall_content\", \"current_text\": \"N/A\", \"suggested_improvement\": \...

Edit
Note: Value is too large to be rendered in the dashboard. Please expand the editor to edit the value

validation_status
varchar
validated

Gemini validation status: pending, validating, completed, failed

is_resume
bool

TRUE

TRUE
Gemini validation result: true if document is a resume, false if not

validation_likelihood
int4
90
Gemini confidence percentage (0-100)

validation_reason
text
Fallback validation - Gemini validation failed

Gemini explanation for validation result

validation_error
text
Validation failed: 403 Permission denied: Consumer 'api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I' has been suspended. [reason: "CONSUMER_SUSPENDED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
metadata {
  key: "containerInfo"
  value: "api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I"
}
metadata {
  key: "consumer"
  value: "projects/417913572512"
}
, locale: "en-US"
message: "Permission denied: Consumer \'api_key:AIzaSyD2KZRVtKBwMRHtPbai5j5JGpZBsJcry5I\' has been suspended."
]

Error message if validation failed

validated_at
timestamp

06/09/2025 11:27:35 PM

Timestamp when validation completed

content_analysis_status
varchar
completed

content_analyzed_at
timestamptz

06/10/2025 02:28:32 AM

Your local timezone will be automatically applied (+0300)