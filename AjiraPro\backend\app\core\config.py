import os
import logging
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
try:
    load_dotenv()
    logger.info("Loaded environment variables from .env file")
except Exception as e:
    logger.warning(f"Failed to load .env file: {str(e)}")

# Log environment variables for debugging (without exposing sensitive values)
logger.info(f"SUPABASE_URL environment variable: {'Set' if os.getenv('SUPABASE_URL') else 'Not set'}")
logger.info(f"SUPABASE_KEY environment variable: {'Set' if os.getenv('SUPABASE_KEY') else 'Not set'}")
logger.info(f"REDIS_URL environment variable: {'Set' if os.getenv('REDIS_URL') else 'Not set'}")
logger.info(f"ENVIRONMENT: {os.getenv('ENVIRONMENT', 'Not set')}")
logger.info(f"DEBUG: {os.getenv('DEBUG', 'Not set')}")

class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "AjiraPro API"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    API_PREFIX: str = "/api"

    # Supabase settings
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY: str = os.getenv("SUPABASE_KEY", "")

    # AI Service settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    CLAUDE_API_KEY: str = os.getenv("CLAUDE_API_KEY", "")
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")

    # AI Service configuration
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4")
    CLAUDE_MODEL: str = os.getenv("CLAUDE_MODEL", "claude-3-5-sonnet-20241022")
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "models/gemini-1.5-flash-latest")
    AI_REQUEST_TIMEOUT: int = int(os.getenv("AI_REQUEST_TIMEOUT", "60"))
    AI_MAX_RETRIES: int = int(os.getenv("AI_MAX_RETRIES", "3"))

    # Redis settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://default:<EMAIL>:6379")

    # Flutterwave settings
    FLUTTERWAVE_SECRET_KEY: str = os.getenv("FLUTTERWAVE_SECRET_KEY", "")
    FLUTTERWAVE_PUBLIC_KEY: str = os.getenv("FLUTTERWAVE_PUBLIC_KEY", "")
    FLUTTERWAVE_ENCRYPTION_KEY: str = os.getenv("FLUTTERWAVE_ENCRYPTION_KEY", "")

    # CORS settings - Handle environment variable override
    @property
    def CORS_ORIGINS(self) -> list:
        # Check if CORS_ORIGINS is set as environment variable
        env_cors = os.getenv("CORS_ORIGINS", "")
        if env_cors:
            logger.warning(f"CORS_ORIGINS environment variable found: {env_cors}")
            # Split by comma and strip whitespace, remove any semicolons and quotes
            origins = []
            for origin in env_cors.split(","):
                cleaned = origin.strip().rstrip(';').strip('"').strip("'")
                if cleaned:
                    origins.append(cleaned)
            # Log each origin separately to avoid formatting issues
            logger.warning(f"Using environment CORS origins (cleaned): {', '.join(origins)}")
            logger.warning(f"Total CORS origins count: {len(origins)}")
            return origins

        # Default comprehensive list
        default_origins = [
            # Development
            "http://localhost:3000",
            "http://localhost:5173",

            # Primary Cloudflare domains (exact from your list)
            "https://fajirapro.pages.dev",
            "https://ajirapro.com",
            "https://ee86f5bd.fajirapro.pages.dev",
            "https://www.ajirapro.com",

            # Domain variations (with and without www, with and without https)
            "https://www.fajirapro.pages.dev",
            "https://ajirapro.pages.dev",
            "https://fajirapro.com",
            "https://www.fajirapro.com",

            # Non-HTTPS variations (for compatibility)
            "http://ajirapro.com",
            "http://www.ajirapro.com",
            "http://fajirapro.com",
            "http://www.fajirapro.com",

            # Bare domains (without protocol)
            "ajirapro.com",
            "www.ajirapro.com",
            "fajirapro.com",
            "www.fajirapro.com",
        ]
        logger.info(f"Using default CORS origins: {default_origins}")
        return default_origins

    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()
