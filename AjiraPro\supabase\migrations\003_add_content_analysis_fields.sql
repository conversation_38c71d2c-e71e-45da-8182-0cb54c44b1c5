-- Add content analysis fields to resumes table
-- This migration adds OpenAI content analysis functionality

-- Add content analysis status and results columns
ALTER TABLE resumes 
ADD COLUMN IF NOT EXISTS content_quality_score DECIMAL(5,2) CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
ADD COLUMN IF NOT EXISTS openai_feedback JSONB,
ADD COLUMN IF NOT EXISTS content_analysis_status VARCHAR(50) DEFAULT 'pending' CHECK (content_analysis_status IN ('pending', 'analyzing', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS content_analyzed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS content_analysis_error TEXT;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_resumes_content_analysis_status ON resumes(content_analysis_status);
CREATE INDEX IF NOT EXISTS idx_resumes_content_analyzed_at ON resumes(content_analyzed_at);

-- Update RLS policies to include new fields
-- The existing RLS policies should automatically cover the new columns since they use SELECT * and UPDATE *

-- Add comments for documentation
COMMENT ON COLUMN resumes.content_quality_score IS 'OpenAI content quality score (0-100)';
COMMENT ON COLUMN resumes.openai_feedback IS 'Structured feedback from OpenAI content analysis';
COMMENT ON COLUMN resumes.content_analysis_status IS 'Status of OpenAI content analysis';
COMMENT ON COLUMN resumes.content_analyzed_at IS 'Timestamp when content analysis was completed';
COMMENT ON COLUMN resumes.content_analysis_error IS 'Error message if content analysis failed';
